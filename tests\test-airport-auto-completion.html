<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机场自动补全功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 12px;
        }
        .test-button {
            background: #007AFF;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-result {
            margin-top: 20px;
            padding: 16px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .test-result.success {
            background: #E8F5E8;
            border: 1px solid #4CAF50;
        }
        .test-result.error {
            background: #FFE8E8;
            border: 1px solid #F44336;
        }
        .scenario {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
        }
        .scenario h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        .scenario p {
            margin: 4px 0;
            color: #666;
        }
        .quick-test {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 12px;
        }
        .quick-test button {
            padding: 8px 16px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }
        .quick-test button:hover {
            background: #e0e0e0;
        }
    </style>
</head>
<body>
    <h1>🛩️ 机场自动补全功能测试</h1>
    <p>测试新添加的智能机场识别和自动补全功能</p>

    <div class="test-container">
        <h3>🔧 测试输入</h3>
        <textarea 
            id="testInput" 
            class="test-input" 
            rows="4" 
            placeholder="输入订单描述，例如：从香格里拉酒店接客人..."
        ></textarea>
        <button class="test-button" onclick="testAirportCompletion()">🚀 测试机场补全</button>
        
        <div class="quick-test">
            <p><strong>快速测试:</strong></p>
            <button onclick="fillTest('从香格里拉酒店接客人')">接机场景1</button>
            <button onclick="fillTest('送客人到新山市中心')">送机场景1</button>
            <button onclick="fillTest('从Hilton酒店接客人去机场')">接机场景2</button>
            <button onclick="fillTest('明天从Raffles新加坡送机')">送机场景2</button>
            <button onclick="fillTest('从槟城Hard Rock接客人')">接机场景3</button>
            <button onclick="fillTest('亚庇Hyatt送机服务')">送机场景3</button>
        </div>
        
        <div id="testResult"></div>
    </div>

    <div class="test-container">
        <h3>📝 预期测试场景</h3>
        
        <div class="scenario">
            <h4>接机场景</h4>
            <p><strong>输入:</strong> "从香格里拉酒店接客人"</p>
            <p><strong>预期:</strong> pickup: "吉隆坡国际机场KLIA", dropoff: "Shangri-La Hotel Kuala Lumpur"</p>
        </div>
        
        <div class="scenario">
            <h4>送机场景</h4>
            <p><strong>输入:</strong> "送客人到新山市中心"</p>
            <p><strong>预期:</strong> pickup: "新山士乃机场", dropoff: "新山市中心"</p>
        </div>
        
        <div class="scenario">
            <h4>新加坡场景</h4>
            <p><strong>输入:</strong> "从Raffles新加坡送机"</p>
            <p><strong>预期:</strong> pickup: "Raffles Singapore", dropoff: "新加坡樟宜机场"</p>
        </div>
        
        <div class="scenario">
            <h4>亚庇场景</h4>
            <p><strong>输入:</strong> "从亚庇Hyatt接机"</p>
            <p><strong>预期:</strong> pickup: "亚庇国际机场", dropoff: "Hyatt Regency Kinabalu"</p>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/adapters/gemini-service-adapter.js"></script>

    <script>
        // 测试函数
        function fillTest(text) {
            document.getElementById('testInput').value = text;
        }

        async function testAirportCompletion() {
            const input = document.getElementById('testInput').value.trim();
            const resultDiv = document.getElementById('testResult');
            
            if (!input) {
                resultDiv.innerHTML = '<div class="test-result error">请输入测试内容</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="test-result">🔄 正在测试机场自动补全...</div>';
            
            try {
                // 检查Gemini服务是否可用
                if (!window.geminiService) {
                    throw new Error('Gemini服务未加载');
                }
                
                console.log('🧪 开始测试:', input);
                
                // 调用Gemini服务解析
                const result = await geminiService.parseOrder(input);
                
                console.log('📊 解析结果:', result);
                
                if (result && result.success && result.data) {
                    let orders = Array.isArray(result.data) ? result.data : [result.data];
                    
                    let output = '✅ 解析成功！\n\n';
                    
                    orders.forEach((order, index) => {
                        output += `订单 ${index + 1}:\n`;
                        output += `- pickup: "${order.pickup || 'null'}"\n`;
                        output += `- dropoff: "${order.dropoff || 'null'}"\n`;
                        output += `- sub_category_id: ${order.sub_category_id} (${getServiceType(order.sub_category_id)})\n`;
                        output += `- customer_name: "${order.customer_name || 'null'}"\n`;
                        output += `- pickup_date: "${order.pickup_date || 'null'}"\n`;
                        output += `- pickup_time: "${order.pickup_time || 'null'}"\n`;
                        
                        // 检查是否自动补全了机场
                        const hasAirport = checkAirportCompletion(order);
                        if (hasAirport.completed) {
                            output += `\n🛩️ 机场自动补全: ${hasAirport.message}\n`;
                        }
                        
                        output += '\n';
                    });
                    
                    resultDiv.innerHTML = `<div class="test-result success">${output}</div>`;
                    
                } else {
                    throw new Error(result?.message || '解析失败，没有返回有效数据');
                }
                
            } catch (error) {
                console.error('❌ 测试失败:', error);
                resultDiv.innerHTML = `<div class="test-result error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        function getServiceType(id) {
            switch(id) {
                case 2: return '接机';
                case 3: return '送机';
                case 4: return '包车';
                default: return '未知';
            }
        }
        
        function checkAirportCompletion(order) {
            const airportKeywords = ['机场', 'Airport', 'KLIA', 'Changi', 'BKI', 'PEN', 'JHB'];
            
            if (order.sub_category_id === 2) { // 接机
                const pickupHasAirport = airportKeywords.some(keyword => 
                    order.pickup?.includes(keyword)
                );
                if (pickupHasAirport) {
                    return {
                        completed: true,
                        message: `接机场景 - 自动补全了出发地机场: ${order.pickup}`
                    };
                }
            }
            
            if (order.sub_category_id === 3) { // 送机
                const dropoffHasAirport = airportKeywords.some(keyword => 
                    order.dropoff?.includes(keyword)
                );
                if (dropoffHasAirport) {
                    return {
                        completed: true,
                        message: `送机场景 - 自动补全了目的地机场: ${order.dropoff}`
                    };
                }
            }
            
            return { completed: false };
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 机场自动补全测试页面已加载');
            
            // 检查依赖
            if (typeof window.getLogger !== 'function') {
                console.warn('⚠️ Logger未加载，可能影响功能');
            }
            
            // 初始化Gemini服务
            if (typeof GeminiService !== 'undefined') {
                window.geminiService = new GeminiService();
                console.log('✅ Gemini服务已初始化');
            } else {
                console.error('❌ Gemini服务类未找到');
            }
        });
    </script>
</body>
</html>