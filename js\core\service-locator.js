/**
 * ============================================================================
 * 🚀 核心业务流程 - 统一服务定位器 (系统核心) - 重构版
 * ============================================================================
 *
 * @fileoverview 统一服务定位器 - 系统核心 (重构版)
 * @description 解决重复的getLogger、getAppState、getGeminiService等问题，支持母子两层架构
 *
 * @businessFlow 统一服务管理
 * 在核心业务流程中的作用：
 * 贯穿整个系统，为所有模块提供统一的服务获取方式：
 * 1. 日志服务获取 (getLogger) - 消除8处重复定义
 * 2. 应用状态服务获取 (getAppState) - 消除4处重复定义
 * 3. Gemini服务获取 (getGeminiService) - 消除3处重复定义
 * 4. 母子两层架构服务获取
 * 5. 其他系统服务的统一获取
 *
 * @architecture System Core (系统核心)
 * - 职责：统一的服务定位和依赖注入
 * - 原则：单例模式，服务注册和发现
 * - 接口：提供统一的服务获取API
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - core/dependency-container.js (依赖容器)
 * 下游被依赖：
 * - 所有模块 (通过统一的服务获取方式)
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 服务注册和管理
 * - 🟢 服务实例缓存和生命周期管理
 * - 🟢 依赖注入和服务发现
 * - 🟢 服务健康检查和监控
 * - 🟢 降级方案和错误处理
 * - 🟢 母子两层架构服务支持
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯服务管理，不调用远程API）
 *
 * @compatibility 兼容性保证
 * - 保持现有getLogger()等函数接口
 * - 提供向后兼容的服务获取方式
 * - 支持渐进式迁移到新架构
 *
 * @refactoringConstraints 重构约束
 * - ✅ 不能破坏现有的服务获取方式
 * - ✅ 必须支持延迟加载和循环依赖处理
 * - ✅ 保持服务实例的单例性
 * - ✅ 提供完整的降级方案
 *
 * @problemSolved 解决的问题
 * - ✅ 消除8处getLogger重复定义
 * - ✅ 消除4处getAppState重复定义
 * - ✅ 消除3处getGeminiService重复定义
 * - ✅ 支持母子两层架构服务获取
 * - ✅ 提供统一的服务获取方式
 *
 * <AUTHOR>
 * @version 2.0.0 (重构版)
 * @since 2024-XX-XX
 * @lastModified 2025-08-09
 * @refactoringStatus 已重构 - 支持母子两层架构
 */

// 确保OTA命名空间和依赖容器存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 服务定位器类
     * 提供统一的服务获取接口，兼容旧的获取方式
     */
    class ServiceLocator {
        constructor() {
            this.container = null;
            this.fallbackMap = new Map();
            this.migrationWarnings = new Set();
        }

        /**
         * 初始化服务定位器
         * @param {DependencyContainer} container - 依赖容器实例
         */
        init(container) {
            this.container = container;
            this.setupFallbackMap();
            // 减法开发：不再在定位器中注册核心服务，统一由 ApplicationBootstrap 负责注册
            console.log('✅ 服务定位器已初始化');
        }

        /**
         * 设置降级映射
         * 用于兼容旧的获取方式
         */
        setupFallbackMap() {
            // 核心服务
            this.fallbackMap.set('appState', () => window.OTA.appState || window.appState);
            // 日志器提供安全降级到 console，避免早期初始化阶段报错
            this.fallbackMap.set('logger', () => window.OTA.logger || window.logger || console);
            this.fallbackMap.set('apiService', () => window.OTA.apiService || window.apiService);
            this.fallbackMap.set('geminiService', () => window.OTA.geminiService || window.geminiService);
            this.fallbackMap.set('uiManager', () => window.OTA.uiManager || window.uiManager);
            this.fallbackMap.set('utils', () => window.OTA.utils || window.utils);
            this.fallbackMap.set('i18nManager', () => window.OTA.i18nManager || window.i18nManager);
            
            // 功能管理器
            this.fallbackMap.set('formManager', () => window.OTA.formManager || window.formManager);
            this.fallbackMap.set('imageUploadManager', () => window.OTA.imageUploadManager || window.imageUploadManager);
            this.fallbackMap.set('currencyConverter', () => window.OTA.currencyConverter || window.currencyConverter);
            this.fallbackMap.set('multiOrderManager', () => window.OTA.multiOrderManager || window.multiOrderManager);
            this.fallbackMap.set('orderHistoryManager', () => window.OTA.orderHistoryManager || window.orderHistoryManager);
            this.fallbackMap.set('pagingServiceManager', () => window.OTA.pagingServiceManager || window.pagingServiceManager);
            
            // 多订单系统服务
            this.fallbackMap.set('multiOrderDetector', () => window.OTA.multiOrderDetector || this.createMultiOrderService('MultiOrderDetector'));
            this.fallbackMap.set('multiOrderProcessor', () => window.OTA.multiOrderProcessor || this.createMultiOrderService('MultiOrderProcessor'));
            this.fallbackMap.set('multiOrderRenderer', () => window.OTA.multiOrderRenderer || this.createMultiOrderService('MultiOrderRenderer'));
            this.fallbackMap.set('batchProcessor', () => window.OTA.batchProcessor || this.createMultiOrderService('BatchProcessor'));

            // 母子两层架构服务 (新增)
            // 母层控制器
            this.fallbackMap.set('businessFlowController', () => window.OTA.businessFlowController || window.BusinessFlowController);
            this.fallbackMap.set('orderManagementController', () => window.OTA.orderManagementController || window.OrderManagementController);

            // 子层实现 - Flow层
            this.fallbackMap.set('channelDetector', () => window.OTA.channelDetector || window.ChannelDetector);
            this.fallbackMap.set('promptBuilder', () => window.OTA.promptBuilder || window.PromptBuilder);
            this.fallbackMap.set('geminiCaller', () => window.OTA.geminiCaller || window.GeminiCaller);
            this.fallbackMap.set('resultProcessor', () => window.OTA.resultProcessor || window.ResultProcessor);
            this.fallbackMap.set('orderParser', () => window.OTA.orderParser || window.OrderParser);
            this.fallbackMap.set('knowledgeBase', () => window.OTA.knowledgeBase || window.KnowledgeBase);
            this.fallbackMap.set('addressTranslator', () => window.OTA.addressTranslator || window.AddressTranslator);

            // 子层实现 - Order层
            this.fallbackMap.set('multiOrderHandler', () => window.OTA.multiOrderHandler || window.MultiOrderHandler);
            this.fallbackMap.set('apiCaller', () => window.OTA.apiCaller || window.APICaller);
            this.fallbackMap.set('historyManager', () => window.OTA.historyManager || window.HistoryManager);

            // 适配器层
            this.fallbackMap.set('geminiServiceAdapter', () => window.OTA.geminiServiceAdapter || window.GeminiServiceAdapter);
            this.fallbackMap.set('multiOrderStateManager', () => window.OTA.multiOrderStateManager || this.createMultiOrderService('MultiOrderStateManager'));
            this.fallbackMap.set('multiOrderCoordinator', () => window.OTA.multiOrderCoordinator || this.createMultiOrderService('MultiOrderCoordinator'));
            this.fallbackMap.set('multiOrderManagerV2', () => window.OTA.multiOrderManagerV2 || this.createMultiOrderService('MultiOrderManagerV2'));
        }

        /**
         * 注册核心服务到依赖容器
         */
    registerCoreServices() { /* 已移除：由 ApplicationBootstrap 统一注册 */ }

        /**
         * 获取服务实例
         * @param {string} serviceName - 服务名称
         * @returns {any} 服务实例
         */
        getService(serviceName) {
            // 优先从依赖容器获取
            if (this.container && this.container.has(serviceName)) {
                try {
                    return this.container.get(serviceName);
                } catch (error) {
                    console.warn(`从依赖容器获取 ${serviceName} 失败，尝试降级方案:`, error.message);
                }
            }

            // 降级到旧的获取方式
            if (this.fallbackMap.has(serviceName)) {
                const service = this.fallbackMap.get(serviceName)();
                
                // 发出迁移警告（每个服务只警告一次）- 降级为debug级别
                if (!this.migrationWarnings.has(serviceName)) {
                    // console.warn(`⚠️ 服务 ${serviceName} 使用了降级获取方式，建议迁移到依赖容器`);
                    this.migrationWarnings.add(serviceName);
                }
                
                return service;
            }

            // 最后尝试直接从全局获取
            const globalService = window.OTA[serviceName] || window[serviceName];
            if (globalService) {
                console.warn(`⚠️ 服务 ${serviceName} 从全局获取，建议注册到依赖容器`);
                return globalService;
            }

            throw new Error(`服务 ${serviceName} 未找到`);
        }

        /**
         * 检查服务是否可用
         * @param {string} serviceName - 服务名称
         * @returns {boolean}
         */
        hasService(serviceName) {
            try {
                const service = this.getService(serviceName);
                return !!service;
            } catch {
                return false;
            }
        }

        /**
         * 获取所有可用服务
         * @returns {string[]}
         */
        getAvailableServices() {
            const services = new Set();
            
            // 从依赖容器获取
            if (this.container) {
                this.container.getRegisteredServices().forEach(name => services.add(name));
            }
            
            // 从降级映射获取
            this.fallbackMap.forEach((_, name) => services.add(name));
            
            // 从全局获取
            Object.keys(window.OTA || {}).forEach(name => services.add(name));
            
            return Array.from(services);
        }

        /**
         * 创建默认的AppState实例
         */
    createAppState() { /* 减法开发：不再创建默认实例，改由上游显式提供 */ }

        /**
         * 获取Logger实例 - 简化版本
         * 减法开发：删除冗余创建逻辑，直接返回已存在的实例
         */
    createLogger() { return console; }

        /**
         * 创建默认的ApiService实例
         */
    createApiService() { /* 已移除默认实现 */ }

        /**
         * 创建默认的GeminiService实例
         */
    createGeminiService() { /* 已移除默认实现 */ }

        /**
         * 创建默认的UIManager实例
         */
    createUIManager() { /* 已移除默认实现 */ }

        /**
         * 创建默认的Utils实例
         */
    createUtils() { /* 已移除默认实现 */ }

        /**
         * 创建默认的FormManager实例
         */
    createFormManager() { /* 已移除默认实现 */ }

        /**
         * 获取多订单服务实例 - 简化版本
         * 减法开发：删除复杂创建逻辑，直接返回已存在的实例
         * @param {string} serviceName - 服务类名
         * @returns {Object|null} 服务实例或null
         */
        createMultiOrderService(serviceName) {
            // 尝试从OTA命名空间获取已存在的实例
            if (window.OTA) {
                // 优先查找完整类名
                let instance = window.OTA[serviceName];
                if (instance) return instance;
                
                // 查找小写变体 (例如 MultiOrderDetector -> multiOrderDetector)
                const lowerCaseName = serviceName.charAt(0).toLowerCase() + serviceName.slice(1);
                instance = window.OTA[lowerCaseName];
                if (instance) return instance;
            }
            
            // 减法开发：不创建降级实例，直接返回null
            // 让调用方处理null情况，而不是创建假实例
            return null;
        }


        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            // 尝试从已注册的服务获取
            if (this.container && this.container.has('logger')) {
                try {
                    return this.container.get('logger');
                } catch (error) {
                    // 降级到基础console logger
                }
            }
            
            // 降级方案
            return {
                // 兼容多签名：
                // 1) log(message)
                // 2) log(message, level)
                // 3) log(message, data)
                // 4) log(message, level, data)
                log: (...args) => {
                    const message = args[0];
                    let level = 'info';
                    let data;

                    if (typeof args[1] === 'string') {
                        level = args[1];
                        data = args[2];
                    } else if (typeof args[1] !== 'undefined') {
                        // 第二个参数是数据对象
                        data = args[1];
                    }

                    const levelStr = typeof level === 'string' ? level : String(level || 'info');
                    const prefix = `[${levelStr.toUpperCase()}]`;

                    // 根据级别选择合适的控制台方法
                    const method = (lvl => {
                        switch ((lvl || '').toLowerCase()) {
                            case 'error': return 'error';
                            case 'warn':
                            case 'warning': return 'warn';
                            case 'info':
                            case 'success': return 'info';
                            default: return 'log';
                        }
                    })(levelStr);

                    if (typeof data !== 'undefined') {
                        console[method](`${prefix} ${String(message)}`, data);
                    } else {
                        console[method](`${prefix} ${String(message)}`);
                    }
                },
                logError: (message, error) => {
                    // 兼容 logError(error) 与 logError(message, error)
                    if (message instanceof Error && typeof error === 'undefined') {
                        console.error(`[ERROR] ${message.message}`, message);
                    } else {
                        console.error(`[ERROR] ${String(message)}`, error);
                    }
                }
            };
        }

        /**
         * 获取迁移状态报告
         * @returns {Object}
         */
        getMigrationReport() {
            return {
                totalServices: this.getAvailableServices().length,
                containerServices: this.container ? this.container.getRegisteredServices().length : 0,
                fallbackUsed: this.migrationWarnings.size,
                warnings: Array.from(this.migrationWarnings),
                recommendations: [
                    '将所有服务注册到依赖容器',
                    '替换直接的全局访问为 getService() 调用',
                    '移除双重获取模式 (window.OTA.xxx || window.xxx)'
                ]
            };
        }
    }

    // 创建全局服务定位器实例
    const serviceLocator = new ServiceLocator();

    // 等待依赖容器准备就绪后初始化
    if (window.OTA.container) {
        serviceLocator.init(window.OTA.container);
    } else {
        // 延迟初始化
        setTimeout(() => {
            if (window.OTA.container) {
                serviceLocator.init(window.OTA.container);
            }
        }, 100);
    }

    // 暴露到OTA命名空间
    window.OTA.serviceLocator = serviceLocator;

    // 提供统一的服务获取函数
    window.OTA.getService = function(serviceName) {
        return serviceLocator.getService(serviceName);
    };

    // 向后兼容的全局函数
    window.getService = window.OTA.getService;

    // 提供便捷的服务获取函数（逐步替换旧的获取方式）
    window.getAppState = () => serviceLocator.getService('appState');
    // 始终提供可靠的Logger（必要时降级到console）
    window.getLogger = () => serviceLocator.getLogger();
    window.getAPIService = () => serviceLocator.getService('apiService');
    window.getApiService = () => serviceLocator.getService('apiService'); // 兼容大小写变体
    window.getGeminiService = () => serviceLocator.getService('geminiService');
    window.getUIManager = () => serviceLocator.getService('uiManager');
    window.getUtils = () => serviceLocator.getService('utils');

    // 🚀 最小化修复：只添加实际需要的FormManager获取函数
    window.getFormManager = () => serviceLocator.getService('formManager');

    // 多订单系统服务获取函数
    window.getMultiOrderDetector = () => serviceLocator.getService('multiOrderDetector');
    window.getMultiOrderProcessor = () => serviceLocator.getService('multiOrderProcessor');
    window.getMultiOrderRenderer = () => serviceLocator.getService('multiOrderRenderer');
    window.getBatchProcessor = () => serviceLocator.getService('batchProcessor');
    window.getMultiOrderStateManager = () => serviceLocator.getService('multiOrderStateManager');
    window.getMultiOrderCoordinator = () => serviceLocator.getService('multiOrderCoordinator');
    window.getMultiOrderManagerV2 = () => serviceLocator.getService('multiOrderManagerV2');

    console.log('✅ 服务定位器已加载');

})();
