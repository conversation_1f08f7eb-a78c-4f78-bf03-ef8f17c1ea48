<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        
        .user-selector {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .user-btn {
            padding: 8px 16px;
            border: 1px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .user-btn.active {
            background: #007bff;
            color: white;
        }
        
        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .permission-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }
        
        .permission-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .permission-details {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        
        .channel-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🔐 权限系统测试</h1>
    
    <div class="test-container">
        <h3>📊 系统状态检查</h3>
        <div id="system-status">正在检查系统状态...</div>
    </div>
    
    <div class="test-container">
        <h3>👤 用户权限测试</h3>
        <p>选择用户测试不同的权限配置：</p>
        <div class="user-selector">
            <button class="user-btn" onclick="testUser('<EMAIL>')">管理员用户</button>
            <button class="user-btn" onclick="testUser('<EMAIL>')">受限用户1</button>
            <button class="user-btn" onclick="testUser('<EMAIL>')">受限用户2</button>
            <button class="user-btn" onclick="testUser('<EMAIL>')">Demo用户</button>
            <button class="user-btn" onclick="testUser('<EMAIL>')">新用户</button>
        </div>
        
        <div id="current-user" class="status info">请选择要测试的用户</div>
        
        <div class="permission-grid">
            <div class="permission-item">
                <div class="permission-title">💰 价格显示权限</div>
                <div id="price-permissions" class="permission-details">等待测试...</div>
            </div>
            
            <div class="permission-item">
                <div class="permission-title">🌐 Paging功能权限</div>
                <div id="paging-permissions" class="permission-details">等待测试...</div>
            </div>
            
            <div class="permission-item">
                <div class="permission-title">📋 渠道选择权限</div>
                <div id="channel-permissions" class="permission-details">等待测试...</div>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>🧪 功能测试</h3>
        <button onclick="testAllUsers()">测试所有用户权限</button>
        <button onclick="testChannelFiltering()">测试渠道过滤</button>
        <button onclick="testPermissionCache()">测试权限缓存</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="test-log" class="log">等待测试...</div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/config/user-permissions-config.js"></script>
    <script src="js/managers/permission-manager.js"></script>
    
    <script>
        let testLog = document.getElementById('test-log');
        let currentTestUser = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            testLog.textContent += logEntry;
            testLog.scrollTop = testLog.scrollHeight;
            
            console.log(`[Permission Test] ${logEntry}`);
        }
        
        function clearLog() {
            testLog.textContent = '日志已清空\n';
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let status = [];
            
            // 检查关键组件是否存在
            if (typeof window.OTA?.config?.USER_PERMISSION_CONFIG !== 'undefined') {
                status.push('✅ 权限配置已加载');
            } else {
                status.push('❌ 权限配置未加载');
            }
            
            if (typeof window.permissionManager !== 'undefined') {
                status.push('✅ 权限管理器已初始化');
            } else {
                status.push('❌ 权限管理器未初始化');
            }
            
            if (typeof getAppState === 'function') {
                status.push('✅ AppState可用');
            } else {
                status.push('❌ AppState不可用');
            }
            
            statusDiv.innerHTML = status.map(s => `<div class="${s.startsWith('✅') ? 'status success' : 'status error'}">${s}</div>`).join('');
            
            log('系统状态检查完成');
            status.forEach(s => log(s.replace('✅ ', '').replace('❌ ', '')));
        }
        
        // 模拟用户登录
        function simulateUserLogin(email) {
            try {
                // 创建模拟用户对象
                const mockUser = {
                    email: email,
                    id: Math.floor(Math.random() * 1000) + 100,
                    name: email.split('@')[0]
                };
                
                // 设置到AppState（如果可用）
                if (typeof getAppState === 'function') {
                    getAppState().set('auth.user', mockUser);
                }
                
                // 设置到全局变量
                window.currentUser = mockUser;
                
                log(`模拟用户登录: ${email}`, 'success');
                return mockUser;
            } catch (error) {
                log(`模拟登录失败: ${error.message}`, 'error');
                return null;
            }
        }
        
        // 测试指定用户的权限
        function testUser(email) {
            try {
                // 更新按钮状态
                document.querySelectorAll('.user-btn').forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
                
                currentTestUser = email;
                
                // 模拟用户登录
                const user = simulateUserLogin(email);
                if (!user) {
                    return;
                }
                
                document.getElementById('current-user').innerHTML = `当前测试用户: <strong>${email}</strong>`;
                
                // 测试权限
                testUserPermissions(email);
                
            } catch (error) {
                log(`测试用户权限失败: ${error.message}`, 'error');
            }
        }
        
        // 测试用户权限
        function testUserPermissions(email) {
            if (!window.permissionManager) {
                log('权限管理器不可用', 'error');
                return;
            }
            
            try {
                // 测试价格权限
                const pricePermissions = window.permissionManager.checkPriceFieldPermissions(email);
                document.getElementById('price-permissions').textContent = JSON.stringify(pricePermissions, null, 2);
                
                // 测试语言权限
                const pagingPermissions = window.permissionManager.checkLanguagePermissions(email);
                document.getElementById('paging-permissions').textContent = JSON.stringify(pagingPermissions, null, 2);
                
                // 测试渠道权限
                const channelPermissions = window.permissionManager.checkChannelPermissions(email);
                document.getElementById('channel-permissions').textContent = JSON.stringify(channelPermissions, null, 2);
                
                // 获取权限统计
                const stats = window.permissionManager.getPermissionStats();
                
                log(`用户权限测试完成: ${email}`, 'success');
                log(`权限统计: ${JSON.stringify(stats, null, 2)}`, 'info');
                
            } catch (error) {
                log(`权限测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试所有用户权限
        function testAllUsers() {
            const testUsers = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];
            
            log('开始测试所有用户权限...', 'info');
            
            testUsers.forEach(email => {
                try {
                    simulateUserLogin(email);
                    const permissions = window.permissionManager.getUserPermissions(email);
                    const hasRestrictions = window.permissionManager.hasAnyRestrictions(permissions);
                    
                    log(`${email}: ${hasRestrictions ? '有限制' : '无限制'}`, hasRestrictions ? 'warning' : 'success');
                } catch (error) {
                    log(`${email}: 测试失败 - ${error.message}`, 'error');
                }
            });
            
            log('所有用户权限测试完成', 'success');
        }
        
        // 测试渠道过滤
        function testChannelFiltering() {
            const allChannels = [
                'Fliggy',
                'JR Coach Credit',
                'JR Coach Cash',
                'UCSI - Cheras',
                'Era Maz Travel',
                'Demo Channel',
                'Other Channel'
            ];
            
            const testUsers = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
            
            log('开始测试渠道过滤功能...', 'info');
            
            testUsers.forEach(email => {
                try {
                    const filteredChannels = window.permissionManager.getFilteredChannels(allChannels, email);
                    log(`${email}: 允许渠道 ${filteredChannels.length}/${allChannels.length}`, 'info');
                    log(`  允许的渠道: ${JSON.stringify(filteredChannels)}`, 'info');
                } catch (error) {
                    log(`${email}: 渠道过滤测试失败 - ${error.message}`, 'error');
                }
            });
            
            log('渠道过滤测试完成', 'success');
        }
        
        // 测试权限缓存
        function testPermissionCache() {
            if (!window.permissionManager) {
                log('权限管理器不可用', 'error');
                return;
            }
            
            log('开始测试权限缓存...', 'info');
            
            const testEmail = '<EMAIL>';
            
            // 第一次调用（应该缓存）
            const start1 = performance.now();
            const permissions1 = window.permissionManager.getUserPermissions(testEmail);
            const time1 = performance.now() - start1;
            
            // 第二次调用（应该使用缓存）
            const start2 = performance.now();
            const permissions2 = window.permissionManager.getUserPermissions(testEmail);
            const time2 = performance.now() - start2;
            
            log(`第一次调用: ${time1.toFixed(2)}ms`, 'info');
            log(`第二次调用: ${time2.toFixed(2)}ms`, 'info');
            log(`缓存加速: ${((time1 - time2) / time1 * 100).toFixed(1)}%`, time2 < time1 ? 'success' : 'warning');
            
            // 清理缓存
            window.permissionManager.clearCache();
            log('缓存已清理', 'info');
            
            log('权限缓存测试完成', 'success');
        }
        
        // 页面加载完成后检查状态
        window.addEventListener('load', () => {
            log('权限系统测试页面已加载', 'info');
            
            // 延迟检查，确保脚本加载完成
            setTimeout(() => {
                checkSystemStatus();
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error?.message || event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>