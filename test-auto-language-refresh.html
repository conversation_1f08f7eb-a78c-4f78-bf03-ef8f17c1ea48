<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Detector 自动刷新测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; border: 1px solid #ffeeba; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        textarea { width: 100%; height: 120px; margin: 10px 0; }
        .log-container { height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
    </style>
</head>
<body>
    <h1>🌐 Language Detector 自动刷新测试</h1>
    
    <div class="test-section">
        <h2>📝 测试说明</h2>
        <div class="result">
            <strong>功能:</strong> 每次触发 realtime-analysis 时，自动刷新 language-detector<br>
            <strong>期望:</strong> 输入文本变化时，语言选择器自动更新匹配的语言<br>
            <strong>测试:</strong> 输入中文/英文内容，观察语言选择器是否自动更新
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 实时测试</h2>
        <label for="testInput">输入测试文本:</label>
        <textarea id="testInput" placeholder="输入订单内容...&#10;例如：&#10;- 中文: 明天早上8点从吉隆坡机场接机&#10;- 英文: Tomorrow 8am pickup from KLIA airport"></textarea>
        
        <div>
            <button onclick="testChineseText()">测试中文文本</button>
            <button onclick="testEnglishText()">测试英文文本</button>
            <button onclick="testMixedText()">测试混合文本</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📊 当前语言状态</h2>
        <button onclick="checkLanguageStatus()">检查当前语言状态</button>
        <div id="languageStatus"></div>
    </div>

    <div class="test-section">
        <h2>📋 操作日志</h2>
        <div id="logContainer" class="log-container"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/core/script-loader.js"></script>
    
    <script>
        let logContainer;
        
        // 等待脚本加载完成
        setTimeout(() => {
            setupTests();
        }, 2000);

        function setupTests() {
            logContainer = document.getElementById('logContainer');
            log('✅ 测试环境初始化完成', 'success');
            
            // 监听输入变化
            const testInput = document.getElementById('testInput');
            testInput.addEventListener('input', handleInputChange);
        }

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function handleInputChange(event) {
            const text = event.target.value;
            if (text.length > 10) { // 防止过于频繁的触发
                log(`📝 输入文本变化: ${text.substring(0, 50)}...`, 'info');
                
                // 模拟触发realtime-analysis
                setTimeout(() => {
                    triggerRealtimeAnalysisTest(text);
                }, 500);
            }
        }

        async function triggerRealtimeAnalysisTest(text) {
            try {
                log('🚀 模拟触发 realtime-analysis...', 'info');
                
                // 检查实时分析管理器是否存在
                const realtimeManager = window.OTA?.realtimeAnalysisManager;
                if (!realtimeManager) {
                    log('❌ RealtimeAnalysisManager 未找到', 'error');
                    return;
                }

                // 检查language detector是否存在
                const languageDetector = window.OTA?.languageDetector;
                if (!languageDetector) {
                    log('❌ LanguageDetector 未找到', 'error');
                    return;
                }

                // 记录刷新前的状态
                const beforeState = getCurrentLanguageState();
                log(`📊 刷新前语言状态: ${beforeState}`, 'info');

                // 手动调用language detector的刷新方法
                if (typeof languageDetector.detectAndApply === 'function') {
                    await languageDetector.detectAndApply(text, 'test-trigger');
                    log('🌐 Language detector 手动刷新完成', 'success');
                    
                    // 记录刷新后的状态
                    setTimeout(() => {
                        const afterState = getCurrentLanguageState();
                        log(`📊 刷新后语言状态: ${afterState}`, 'success');
                        
                        if (beforeState !== afterState) {
                            log('✅ 语言状态已更新！', 'success');
                        } else {
                            log('⚠️ 语言状态未变化', 'warning');
                        }
                    }, 100);
                } else {
                    log('❌ detectAndApply 方法不存在', 'error');
                }

            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function getCurrentLanguageState() {
            try {
                // 检查语言选择器的状态
                const languageElements = document.querySelectorAll('input[name="languagesIdArray"]:checked');
                const selectedLanguages = Array.from(languageElements).map(el => el.value);
                return selectedLanguages.length > 0 ? selectedLanguages.join(',') : '无选择';
            } catch (error) {
                return '获取失败';
            }
        }

        function testChineseText() {
            const chineseText = "明天早上8点从吉隆坡国际机场接机，目的地是市中心的希尔顿酒店，共2位乘客，有2件行李。";
            document.getElementById('testInput').value = chineseText;
            log('🇨🇳 测试中文文本', 'info');
            triggerRealtimeAnalysisTest(chineseText);
        }

        function testEnglishText() {
            const englishText = "Tomorrow 8am pickup from Kuala Lumpur International Airport to Hilton Hotel in city center, 2 passengers with 2 luggage.";
            document.getElementById('testInput').value = englishText;
            log('🇬🇧 测试英文文本', 'info');
            triggerRealtimeAnalysisTest(englishText);
        }

        function testMixedText() {
            const mixedText = "Tomorrow 明天 8am pickup from KLIA 吉隆坡机场 to 希尔顿酒店 Hilton Hotel";
            document.getElementById('testInput').value = mixedText;
            log('🌐 测试混合文本', 'info');
            triggerRealtimeAnalysisTest(mixedText);
        }

        function checkLanguageStatus() {
            const statusDiv = document.getElementById('languageStatus');
            const currentState = getCurrentLanguageState();
            
            statusDiv.innerHTML = `
                <div class="result">
                    <strong>当前选中的语言:</strong> ${currentState}<br>
                    <strong>Language Detector 状态:</strong> ${window.OTA?.languageDetector ? '✅ 已加载' : '❌ 未加载'}<br>
                    <strong>Realtime Analysis Manager 状态:</strong> ${window.OTA?.realtimeAnalysisManager ? '✅ 已加载' : '❌ 未加载'}
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('languageStatus').innerHTML = '';
            logContainer.innerHTML = '';
            log('🧹 结果已清空', 'info');
        }
    </script>
</body>
</html>