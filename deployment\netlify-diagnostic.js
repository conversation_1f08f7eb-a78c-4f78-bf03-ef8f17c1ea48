#!/usr/bin/env node

/**
 * Netlify 部署诊断脚本
 * 诊断为什么文件没有被正确部署
 */

const fs = require('fs');
const path = require('path');

class NetlifyDeploymentDiagnostic {
    constructor() {
        this.projectRoot = path.dirname(__dirname);
        this.issues = [];
        this.recommendations = [];
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = type === 'error' ? '🔴' : type === 'warning' ? '🟡' : type === 'success' ? '🟢' : 'ℹ️';
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    checkGitStatus() {
        this.log('检查 Git 状态...', 'info');
        
        const { execSync } = require('child_process');
        
        try {
            // 检查是否有未提交的更改
            const status = execSync('git status --porcelain', { 
                cwd: this.projectRoot, 
                encoding: 'utf8' 
            });
            
            if (status.trim()) {
                this.issues.push('Git 仓库有未提交的更改');
                this.recommendations.push('运行 git add . && git commit -m "更新文件" && git push');
                this.log('发现未提交的更改', 'warning');
                console.log(status);
            } else {
                this.log('Git 状态清洁，所有更改已提交', 'success');
            }
            
            // 检查远程同步状态
            try {
                const behind = execSync('git rev-list HEAD..origin/main --count', {
                    cwd: this.projectRoot,
                    encoding: 'utf8'
                }).trim();
                
                const ahead = execSync('git rev-list origin/main..HEAD --count', {
                    cwd: this.projectRoot,
                    encoding: 'utf8'
                }).trim();
                
                if (parseInt(ahead) > 0) {
                    this.issues.push(`本地领先远程 ${ahead} 个提交`);
                    this.recommendations.push('运行 git push 推送更改到远程仓库');
                    this.log(`本地领先远程 ${ahead} 个提交`, 'warning');
                }
                
                if (parseInt(behind) > 0) {
                    this.issues.push(`本地落后远程 ${behind} 个提交`);
                    this.recommendations.push('运行 git pull 拉取远程更改');
                    this.log(`本地落后远程 ${behind} 个提交`, 'warning');
                }
                
                if (parseInt(ahead) === 0 && parseInt(behind) === 0) {
                    this.log('本地与远程同步', 'success');
                }
                
            } catch (error) {
                this.log('无法检查远程同步状态，可能是远程仓库不存在', 'warning');
            }
            
        } catch (error) {
            this.issues.push('无法检查 Git 状态');
            this.log(`Git 状态检查失败: ${error.message}`, 'error');
        }
    }

    checkNetlifyConfig() {
        this.log('检查 Netlify 配置...', 'info');
        
        const netlifyConfigPath = path.join(this.projectRoot, 'netlify.toml');
        
        if (!fs.existsSync(netlifyConfigPath)) {
            this.issues.push('netlify.toml 文件不存在');
            this.recommendations.push('创建 netlify.toml 配置文件');
            return;
        }

        try {
            const config = fs.readFileSync(netlifyConfigPath, 'utf8');
            this.log('netlify.toml 配置内容:', 'info');
            console.log('--- netlify.toml ---');
            console.log(config);
            console.log('--- 配置结束 ---');
            
            // 检查发布目录
            if (config.includes('publish = "."')) {
                this.log('发布目录设置为当前目录 (.)', 'success');
            } else {
                this.issues.push('发布目录配置可能有误');
                this.recommendations.push('确保 netlify.toml 中 publish = "."');
            }
            
        } catch (error) {
            this.issues.push(`读取 netlify.toml 失败: ${error.message}`);
        }
    }

    checkEssentialFiles() {
        this.log('检查关键文件...', 'info');
        
        const essentialFiles = [
            'index.html',
            'css/main.css',
            'js/app-state.js',
            'js/adapters/gemini-service-adapter.js'
        ];
        
        let allFilesExist = true;
        
        essentialFiles.forEach(file => {
            const filePath = path.join(this.projectRoot, file);
            if (fs.existsSync(filePath)) {
                this.log(`✓ ${file} 存在`, 'success');
            } else {
                this.log(`✗ ${file} 不存在`, 'error');
                this.issues.push(`关键文件不存在: ${file}`);
                allFilesExist = false;
            }
        });
        
        if (allFilesExist) {
            this.log('所有关键文件都存在', 'success');
        }
    }

    checkFilePermissions() {
        this.log('检查文件权限...', 'info');
        
        const indexPath = path.join(this.projectRoot, 'index.html');
        
        try {
            const stats = fs.statSync(indexPath);
            this.log(`index.html 文件大小: ${stats.size} bytes`, 'info');
            this.log(`index.html 最后修改: ${stats.mtime}`, 'info');
            
            if (stats.size === 0) {
                this.issues.push('index.html 文件为空');
                this.recommendations.push('检查 index.html 文件内容');
            }
            
        } catch (error) {
            this.issues.push(`无法读取 index.html 文件信息: ${error.message}`);
        }
    }

    checkDeploymentHistory() {
        this.log('检查部署历史建议...', 'info');
        
        this.recommendations.push('在 Netlify 后台检查部署日志');
        this.recommendations.push('确认 Git 仓库已正确连接到 Netlify');
        this.recommendations.push('检查 Netlify 项目设置中的构建配置');
        this.recommendations.push('尝试手动触发部署');
    }

    generateFixScript() {
        this.log('生成修复脚本...', 'info');
        
        const fixScript = `#!/bin/bash
# Netlify 部署修复脚本

echo "开始修复 Netlify 部署问题..."

# 1. 确保所有文件已提交
git add .
git commit -m "修复部署：确保所有文件已提交"

# 2. 推送到远程仓库
git push origin main

# 3. 验证关键文件
if [ ! -f "index.html" ]; then
    echo "错误: index.html 不存在"
    exit 1
fi

if [ ! -f "netlify.toml" ]; then
    echo "错误: netlify.toml 不存在"
    exit 1
fi

echo "修复完成！请在 Netlify 后台手动触发部署"
echo "或等待自动部署完成"
`;
        
        const fixScriptPath = path.join(this.projectRoot, 'deployment', 'fix-deployment.sh');
        fs.writeFileSync(fixScriptPath, fixScript);
        
        const fixScriptBat = `@echo off
REM Netlify 部署修复脚本 (Windows)

echo 开始修复 Netlify 部署问题...

REM 1. 确保所有文件已提交
git add .
git commit -m "修复部署：确保所有文件已提交"

REM 2. 推送到远程仓库
git push origin main

REM 3. 验证关键文件
if not exist "index.html" (
    echo 错误: index.html 不存在
    exit /b 1
)

if not exist "netlify.toml" (
    echo 错误: netlify.toml 不存在
    exit /b 1
)

echo 修复完成！请在 Netlify 后台手动触发部署
echo 或等待自动部署完成
pause
`;
        
        const fixScriptBatPath = path.join(this.projectRoot, 'deployment', 'fix-deployment.bat');
        fs.writeFileSync(fixScriptBatPath, fixScriptBat);
        
        this.log(`修复脚本已生成: ${fixScriptPath}`, 'success');
        this.log(`修复脚本已生成: ${fixScriptBatPath}`, 'success');
    }

    generateReport() {
        this.log('生成诊断报告...', 'info');
        
        const report = {
            timestamp: new Date().toISOString(),
            issues: this.issues,
            recommendations: this.recommendations,
            status: this.issues.length === 0 ? 'HEALTHY' : 'NEEDS_ATTENTION'
        };
        
        const reportPath = path.join(this.projectRoot, 'netlify-deployment-diagnostic.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // 生成人类可读的报告
        const humanReport = `
# Netlify 部署诊断报告

生成时间: ${new Date().toLocaleString()}

## 问题总结
${this.issues.length === 0 ? '✅ 未发现问题' : `❌ 发现 ${this.issues.length} 个问题:`}
${this.issues.map(issue => `- ${issue}`).join('\n')}

## 建议操作
${this.recommendations.map(rec => `- ${rec}`).join('\n')}

## 常见解决方案

### 1. 文件未上传问题
通常是因为文件没有提交到 Git 或没有推送到远程仓库。

解决方案:
\`\`\`bash
git add .
git commit -m "部署修复"
git push origin main
\`\`\`

### 2. Netlify 配置问题
检查 netlify.toml 配置是否正确。

### 3. 手动触发部署
在 Netlify 后台点击 "Trigger deploy" 手动触发部署。

### 4. 检查构建日志
在 Netlify 后台查看详细的构建和部署日志。
`;
        
        const humanReportPath = path.join(this.projectRoot, 'netlify-deployment-diagnostic.md');
        fs.writeFileSync(humanReportPath, humanReport);
        
        this.log(`诊断报告已生成: ${reportPath}`, 'success');
        this.log(`人类可读报告: ${humanReportPath}`, 'success');
        
        return report;
    }

    run() {
        this.log('开始 Netlify 部署诊断...', 'info');
        
        this.checkGitStatus();
        this.checkNetlifyConfig();
        this.checkEssentialFiles();
        this.checkFilePermissions();
        this.checkDeploymentHistory();
        this.generateFixScript();
        
        const report = this.generateReport();
        
        this.log('诊断完成', 'info');
        this.log(`发现问题: ${this.issues.length}`, this.issues.length > 0 ? 'warning' : 'success');
        this.log(`建议操作: ${this.recommendations.length}`, 'info');
        
        if (this.issues.length > 0) {
            this.log('需要处理的问题:', 'warning');
            this.issues.forEach(issue => this.log(`  - ${issue}`, 'warning'));
        }
        
        if (this.recommendations.length > 0) {
            this.log('建议执行的操作:', 'info');
            this.recommendations.forEach(rec => this.log(`  - ${rec}`, 'info'));
        }
        
        return report.status === 'HEALTHY';
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const diagnostic = new NetlifyDeploymentDiagnostic();
    const isHealthy = diagnostic.run();
    process.exit(isHealthy ? 0 : 1);
}

module.exports = NetlifyDeploymentDiagnostic;
