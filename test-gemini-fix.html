<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 Gemini API修复测试</h1>
    
    <div class="test-container">
        <h3>📊 系统状态检查</h3>
        <div id="status-check">正在检查系统状态...</div>
    </div>
    
    <div class="test-container">
        <h3>🧪 API调用测试</h3>
        <p>输入测试订单信息：</p>
        <textarea id="test-input" placeholder="粘贴订单信息进行测试...">订单编号：2872865460249204057
买家：山转水转1
支付时间：2025-08-11 08:51:26

经济7座
【送机】
新加坡-新加坡
[出发]新加坡市中豪亚酒店
[抵达]樟宜机场T1
约26.4公里
2025-08-11 14:10:00
联系人许丽俊
真实号：13916811351</textarea>
        <button onclick="testAPICall()">测试API调用</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="test-container">
        <h3>📋 测试日志</h3>
        <div id="test-log" class="log">等待测试...</div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/flow/gemini-caller.js"></script>
    
    <script>
        let testLog = document.getElementById('test-log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            testLog.textContent += logEntry;
            testLog.scrollTop = testLog.scrollHeight;
            
            console.log(`[Gemini Test] ${logEntry}`);
        }
        
        function clearLog() {
            testLog.textContent = '日志已清空\n';
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('status-check');
            let status = [];
            
            // 检查关键组件是否存在
            if (typeof window.OTA !== 'undefined') {
                status.push('✅ OTA命名空间存在');
            } else {
                status.push('❌ OTA命名空间缺失');
            }
            
            if (typeof window.OTA?.GeminiCaller !== 'undefined') {
                status.push('✅ GeminiCaller类已加载');
            } else {
                status.push('❌ GeminiCaller类未加载');
            }
            
            if (typeof window.OTA?.geminiCaller !== 'undefined') {
                status.push('✅ GeminiCaller实例存在');
                
                // 获取API状态
                try {
                    const apiStatus = window.OTA.geminiCaller.getAPIStatus();
                    status.push(`✅ API配置: ${apiStatus.model}`);
                    status.push(`✅ 超时设置: ${apiStatus.baseTimeout}ms`);
                    status.push(`✅ 最大重试: ${apiStatus.maxRetries}次`);
                } catch (error) {
                    status.push(`❌ API状态获取失败: ${error.message}`);
                }
            } else {
                status.push('❌ GeminiCaller实例缺失');
            }
            
            statusDiv.innerHTML = status.map(s => `<div class="${s.startsWith('✅') ? 'status success' : 'status error'}">${s}</div>`).join('');
            
            log('系统状态检查完成');
            status.forEach(s => log(s.replace('✅ ', '').replace('❌ ', '')));
        }
        
        // 测试API调用
        async function testAPICall() {
            const input = document.getElementById('test-input').value.trim();
            
            if (!input) {
                log('请输入测试数据', 'error');
                return;
            }
            
            log('开始API调用测试...');
            log(`输入数据长度: ${input.length}字符`);
            
            try {
                if (!window.OTA?.geminiCaller) {
                    throw new Error('GeminiCaller实例不存在');
                }
                
                log('正在调用Gemini API...');
                
                // 创建测试提示词
                const testPrompt = `请分析以下订单信息：\n\n${input}\n\n请返回JSON格式的解析结果。`;
                
                // 调用API
                const result = await window.OTA.geminiCaller.callAPI(testPrompt, 'text', {
                    isRealtime: false
                });
                
                log('✅ API调用成功！', 'success');
                log(`返回结果类型: ${typeof result}`);
                
                if (result) {
                    log('返回结果内容:');
                    log(JSON.stringify(result, null, 2));
                } else {
                    log('⚠️ 返回结果为空', 'warning');
                }
                
            } catch (error) {
                log(`❌ API调用失败: ${error.message}`, 'error');
                
                // 记录错误详情
                if (error.stack) {
                    log('错误堆栈:');
                    log(error.stack);
                }
            }
        }
        
        // 页面加载完成后检查状态
        window.addEventListener('load', () => {
            log('页面加载完成，开始检查系统状态...');
            
            // 延迟检查，确保脚本加载完成
            setTimeout(() => {
                checkSystemStatus();
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error?.message || event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>