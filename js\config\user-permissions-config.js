/**
 * ============================================================================
 * 🔐 用户权限配置文件
 * ============================================================================
 * 
 * @fileoverview 统一的用户权限配置管理
 * @description 集中管理所有用户的权限设置，包括价格显示、功能选项和渠道访问权限
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-12
 * @lastModified 2025-08-12
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.config = window.OTA.config || {};

(function() {
    'use strict';

    /**
     * 完整用户账号列表
     * 从系统后台数据同步，包含ID、姓名、邮箱等信息
     */
    const COMPLETE_USER_LIST = [
        // 系统管理员
        { id: 1, name: 'Super Admin', email: '', phone: '', role_id: 1 },

        // 核心用户
        { id: 37, name: 'smw', email: '<EMAIL>', phone: '0162234711', role_id: 2 },
        { id: 420, name: 'chongyoonlim', email: '<EMAIL>', phone: '0167112699', role_id: 2 },
        { id: 533, name: 'xhs', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 1201, name: 'KK Lucas', email: '<EMAIL>', phone: '+601153392333', role_id: 2 },
        { id: 2446, name: 'UCSI - Cheras', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 2666, name: 'JRCoach', email: '<EMAIL>', phone: '', role_id: 2 },

        // 新增活跃用户
        { id: 2793, name: 'eramaztravel', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 2788, name: 'kai -JB', email: '<EMAIL>', phone: '+60167878373', role_id: 2 },
        { id: 2766, name: 'demo', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 2765, name: 'Mytravelexpert', email: '<EMAIL>', phone: '+60167788740', role_id: 2 },
        { id: 2732, name: 'oceanblue', email: '<EMAIL>', phone: '+60127697117', role_id: 2 },

        // 现有用户
        { id: 89, name: 'GMH Sabah', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 310, name: 'Jcy', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 311, name: 'opAnnie', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 312, name: 'opVenus', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 313, name: 'opEric', email: '', phone: '', role_id: 2 },
        { id: 342, name: 'SMW Wendy', email: 'SMW <EMAIL>', phone: '', role_id: 2 },
        { id: 343, name: 'SMW XiaoYu', email: 'SMW <EMAIL>', phone: '', role_id: 2 },
        { id: 421, name: 'josua', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 428, name: 'Gomyhire Yong', email: '', phone: '', role_id: 2 },
        { id: 550, name: 'test', email: '', phone: '', role_id: 2 },
        { id: 622, name: 'CsBob', email: '', phone: '', role_id: 2 },
        { id: 777, name: '空空', email: '空空@gomyhire.com', phone: '', role_id: 2 },
        { id: 812, name: '淼淼', email: '', phone: '', role_id: 2 },
        { id: 856, name: 'GMH Ashley', email: '', phone: '', role_id: 2 },
        { id: 907, name: 'OP XINYIN', email: '', phone: '', role_id: 2 },
        { id: 1043, name: 'Billy Yong close', email: '', phone: '', role_id: 2 },
        { id: 1047, name: 'OP QiJun', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 1181, name: 'Op Karen', email: '<EMAIL>', phone: '', role_id: 2 },
        { id: 1223, name: 'Chong admin', email: '', phone: '', role_id: 2 },
        { id: 1652, name: 'CSteam Swee Qing', email: 'Swee <EMAIL>', phone: '', role_id: 2 },
        { id: 1832, name: 'GMH SG William', email: '', phone: '', role_id: 2 },
        { id: 2050, name: 'agent victor', email: '', phone: '', role_id: 2 },
        { id: 2085, name: 'CSteam Tze Ying', email: '', phone: '', role_id: 2 },
        { id: 2141, name: 'SMW Nas', email: '', phone: '', role_id: 2 },
        { id: 2142, name: 'SMW Wen', email: '', phone: '', role_id: 2 },
        { id: 2248, name: 'GMH Shi Wei', email: '', phone: '', role_id: 2 },
        { id: 2249, name: 'Skymirror jetty', email: 'Skymirror <EMAIL>', phone: '', role_id: 2 },
        { id: 2340, name: 'GMH JingSoon', email: '', phone: '', role_id: 2 },
        { id: 2358, name: 'GMH Zilok', email: '', phone: '', role_id: 2 },
        { id: 2503, name: 'GMH Veron', email: '', phone: '', role_id: 2 },
        { id: 2595, name: 'Admin Pua', email: '', phone: '', role_id: 2 }
    ];

    /**
     * 完整OTA渠道列表
     * 基于系统中所有可用的OTA渠道
     */
    const COMPLETE_CHANNEL_LIST = [
        // 核心OTA平台
        'Klook West Malaysia', 'Klook Singapore', 'Kkday', 'Ctrip West Malaysia', 'Ctrip API',
        '携程专车', '携程商铺 - CN', 'Fliggy', 'Traveloka', 'Heycar', 'Mozio',

        // SMW相关渠道
        'SMW Eric', 'Smw Wilson', 'Smw Josua', 'Smw Jcyap', 'Smw Vivian Lim', 'Smw Wendy', 
        'Smw Annie', 'SMW Xiaohongshu', 'SMW Whatsapp', 'SMW Agent', 'SMW Walk In', 'SMW Driver Walk-In Com',

        // GMH团队渠道
        'GMH Sabah', '随程-GMH Sabah', 'GMH Terry', 'GMH Ms Yong', 'GMH Ashley', 'GMH Calvin',
        'GMH May', 'GMH Daniel Fong', 'GMH BNI', 'GMH SQ', 'GMH Jiahui', 'GMH Vikki', 'GMH Qijun',
        'GMH Venus', 'GMH Karen', 'GMH Cynthia B10', 'GMH Cynthia', 'GMH Jing Soon', 'GMH Driver',
        'GMH Xiaoxuan', 'GMH Vivian B2B', 'GMH Ads', 'GoMyHire - KL', 'GoMyHire Webpage', 'Gomyhire Pohchengfatt',

        // JR Coach Services系列
        'JR Coach Credit', 'JR Coach Cash',
        'JR COACH SERVICES - C1', 'JR COACH SERVICES - HTP - C1', 'JR COACH SERVICES - GTV - C1',
        'JR COACH SERVICES - JRV - C1', 'JR COACH SERVICES - WYNN - C1', 'JR COACH SERVICES - EJH - C1',

        // 酒店合作伙伴
        'Hotel - Padibox Homestay', 'Hotel - Padi Sentral Homestay', 'Hotel - Secret Garden Homestay',
        'Hotel - Leshore Hotel', 'Hotel - VI Boutique', 'Hotel - East Sun Hotel', 'The Pearl Kuala Lumpur Hotel',
        'Le Méridien Putrajaya', 'ONE18 Boutique Hotel', 'Bintang Collectionz Hotel',

        // MapleHome系列
        'MapleHome - The Robertson KL', 'MapleHome - Swiss Garden Kuala Lumpur',
        'MapleHome - D\'Majestic Premier Suites Kuala Lumpur', 'MapleHome- Chambers Premier Suites Kuala Lumpur',
        'MapleHome - Geo38 Premier Suites Kuala Lumpur', 'MapleHome - The Apple Premier Suites Melaka',
        'MapleHome - Amber Cove Premier Suites Melaka', 'The Maple Suite - Bukit Bintang',

        // Ocean Blue系列
        'Ocean Blue - JC TRAVEL SDN BHD - TC2', 'Ocean Blue - JC TRAVEL SDN BHD QR',

        // 旅行社和代理商
        'B2B Lewis', 'B TN Holiday Sdn Bhd-Eunice', 'Chong Dealer', 'Jing Ge', 'Jing Ge Htp',
        'YenNei', 'EHTT 徐杰', 'Joydeer', 'KL Eric', 'Co-operate Stan', '7deer Travel', 'Columbia',
        'Asia Trail', 'Good Earth Travel', 'Thousand Travel', 'Sabah Adventure', '全景旅游',
        'M.I.C.E Tour', 'Mytravelexpert - TC1', 'Eramaz Travel C1', '上海佳禾',

        // UCSI系列
        'UCSI - Cheras', 'UCSI - Port Dickson', 'Student Travel',

        // 其他专属渠道
        'Kai - TC1', 'Demo'
    ];

    /**
     * 用户权限配置
     * 支持基于邮箱和用户ID的权限控制
     */
    const USER_PERMISSION_CONFIG = {
        
        /**
         * 受限用户配置
         * 键可以是邮箱地址（小写）或用户ID（数字/字符串）
         */
        restrictedUsers: {
            // <EMAIL> (ID: 2793) - 价格：false, paging：false, 渠道受限
            '<EMAIL>': {
                priceDisplay: {
                    canViewOtaPrice: false,
                    canViewDriverFee: false
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Eramaz Travel C1'
                    ]
                }
            },
            2793: { // 同上，基于用户ID
                priceDisplay: {
                    canViewOtaPrice: false,
                    canViewDriverFee: false
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Eramaz Travel C1'
                    ]
                }
            },

            // <EMAIL> (ID: 2788) - 价格：true, paging：false, 渠道受限
            '<EMAIL>': {
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Kai - TC1'
                    ]
                }
            },
            2788: { // 同上，基于用户ID
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Kai - TC1'
                    ]
                }
            },

            // <EMAIL> (ID: 2766) - 价格：true, paging：true, 渠道受限
            '<EMAIL>': {
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: true
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Demo'
                    ]
                }
            },
            2766: { // 同上，基于用户ID
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: true
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Demo'
                    ]
                }
            },

            // <EMAIL> (ID: 2765) - 价格：false, paging：false, 渠道受限
            '<EMAIL>': {
                priceDisplay: {
                    canViewOtaPrice: false,
                    canViewDriverFee: false
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Mytravelexpert - TC1'
                    ]
                }
            },
            2765: { // 同上，基于用户ID
                priceDisplay: {
                    canViewOtaPrice: false,
                    canViewDriverFee: false
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Mytravelexpert - TC1'
                    ]
                }
            },

            // <EMAIL> (ID: 2732) - 价格：false, paging：false, 渠道受限
            '<EMAIL>': {
                priceDisplay: {
                    canViewOtaPrice: false,
                    canViewDriverFee: false
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Ocean Blue - JC TRAVEL SDN BHD - TC2'
                    ]
                }
            },
            2732: { // 同上，基于用户ID
                priceDisplay: {
                    canViewOtaPrice: false,
                    canViewDriverFee: false
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Ocean Blue - JC TRAVEL SDN BHD - TC2'
                    ]
                }
            },

            // <EMAIL> - 价格：true, paging：true, 渠道受限
            '<EMAIL>': {
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: true
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'JR COACH SERVICES - C1',
                        'JR COACH SERVICES - HTP - C1',
                        'JR COACH SERVICES - GTV - C1',
                        'JR COACH SERVICES - JRV - C1',
                        'JR COACH SERVICES - WYNN - C1',
                        'JR COACH SERVICES - EJH - C1'
                    ]
                }
            },
            2666: { // JR Coach 用户ID - 同上
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: true
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'JR COACH SERVICES - C1',
                        'JR COACH SERVICES - HTP - C1',
                        'JR COACH SERVICES - GTV - C1',
                        'JR COACH SERVICES - JRV - C1',
                        'JR COACH SERVICES - WYNN - C1',
                        'JR COACH SERVICES - EJH - C1'
                    ]
                }
            },

            // <EMAIL> (ID: 2446) - 价格：false, paging：false, 渠道受限
            '<EMAIL>': {
                priceDisplay: {
                    canViewOtaPrice: false,
                    canViewDriverFee: false
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'UCSI - Cheras',
                        'UCSI - Port Dickson'
                    ]
                }
            },
            2446: { // 同上，基于用户ID
                priceDisplay: {
                    canViewOtaPrice: false,
                    canViewDriverFee: false
                },
                features: {
                    canUsePaging: false
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'UCSI - Cheras',
                        'UCSI - Port Dickson'
                    ]
                }
            },

            // <EMAIL> (ID: 420) - 价格：true, paging：true, 渠道受限
            '<EMAIL>': {
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: true
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Chong Dealer'
                    ]
                }
            },
            420: { // 同上，基于用户ID
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: true
                },
                channels: {
                    restricted: true,
                    allowedChannels: [
                        'Chong Dealer'
                    ]
                }
            }

            // <EMAIL> (ID: 37) - 价格：true, paging：true, 渠道：无限制
            // 此用户使用默认权限（无限制），无需在restrictedUsers中配置
        },

        /**
         * 默认权限配置
         * 适用于所有未在 restrictedUsers 中明确配置的用户
         */
        defaultPermissions: {
            priceDisplay: {
                canViewOtaPrice: true,
                canViewDriverFee: true
            },
            features: {
                canUsePaging: true
            },
            channels: {
                restricted: false,
                allowedChannels: null // null 表示允许所有渠道
            }
        },

        /**
         * 权限检查配置
         */
        config: {
            // 是否启用权限系统
            enabled: true,
            
            // 是否区分大小写（邮箱检查）
            caseSensitive: false,
            
            // 调试模式
            debugMode: false,
            
            // 权限缓存时间（毫秒）
            cacheTimeout: 5 * 60 * 1000, // 5分钟
            
            // 版本信息
            version: '2.0.0',
            lastUpdated: '2025-08-12'
        }
    };

    /**
     * 权限模板 - 便于快速配置新用户
     */
    const PERMISSION_TEMPLATES = {
        // 完全受限模板
        FULLY_RESTRICTED: {
            priceDisplay: {
                canViewOtaPrice: false,
                canViewDriverFee: false
            },
            features: {
                canUsePaging: false
            },
            channels: {
                restricted: true,
                allowedChannels: []
            }
        },

        // 部分受限模板（只限制价格）
        PRICE_RESTRICTED: {
            priceDisplay: {
                canViewOtaPrice: false,
                canViewDriverFee: false
            },
            features: {
                canUsePaging: true
            },
            channels: {
                restricted: false,
                allowedChannels: null
            }
        },

        // 渠道受限模板
        CHANNEL_RESTRICTED: {
            priceDisplay: {
                canViewOtaPrice: true,
                canViewDriverFee: true
            },
            features: {
                canUsePaging: true
            },
            channels: {
                restricted: true,
                allowedChannels: [] // 需要手动指定
            }
        },

        // 完全权限模板
        FULL_ACCESS: {
            priceDisplay: {
                canViewOtaPrice: true,
                canViewDriverFee: true
            },
            features: {
                canUsePaging: true
            },
            channels: {
                restricted: false,
                allowedChannels: null
            }
        }
    };

    /**
     * 工具函数 - 规范化用户标识
     * @param {string|number} userIdentifier - 用户邮箱或ID
     * @returns {string|number} 规范化后的标识
     */
    function normalizeUserIdentifier(userIdentifier) {
        if (typeof userIdentifier === 'string') {
            return USER_PERMISSION_CONFIG.config.caseSensitive 
                ? userIdentifier 
                : userIdentifier.toLowerCase();
        }
        return userIdentifier;
    }

    /**
     * 获取用户权限配置
     * @param {string|number} userIdentifier - 用户邮箱或ID
     * @returns {object} 用户权限配置
     */
    function getUserPermissions(userIdentifier) {
        if (!USER_PERMISSION_CONFIG.config.enabled) {
            return USER_PERMISSION_CONFIG.defaultPermissions;
        }

        const normalizedId = normalizeUserIdentifier(userIdentifier);
        
        // 检查是否有特定配置
        if (USER_PERMISSION_CONFIG.restrictedUsers[normalizedId]) {
            return USER_PERMISSION_CONFIG.restrictedUsers[normalizedId];
        }

        // 返回默认权限
        return USER_PERMISSION_CONFIG.defaultPermissions;
    }

    /**
     * 检查用户是否有特定权限
     * @param {string|number} userIdentifier - 用户标识
     * @param {string} permissionPath - 权限路径，如 'priceDisplay.canViewOtaPrice'
     * @returns {boolean} 是否有权限
     */
    function hasPermission(userIdentifier, permissionPath) {
        const userPermissions = getUserPermissions(userIdentifier);
        
        // 按路径获取权限值
        const pathParts = permissionPath.split('.');
        let value = userPermissions;
        
        for (const part of pathParts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            } else {
                return false;
            }
        }
        
        return Boolean(value);
    }

    /**
     * 获取用户允许的渠道列表
     * @param {string|number} userIdentifier - 用户标识
     * @returns {array|null} 允许的渠道列表，null表示允许所有
     */
    function getAllowedChannels(userIdentifier) {
        const userPermissions = getUserPermissions(userIdentifier);
        
        if (!userPermissions.channels.restricted) {
            return null; // 允许所有渠道
        }
        
        return userPermissions.channels.allowedChannels || [];
    }

    // 导出到全局作用域
    window.OTA.config.COMPLETE_USER_LIST = COMPLETE_USER_LIST;
    window.OTA.config.COMPLETE_CHANNEL_LIST = COMPLETE_CHANNEL_LIST;
    window.OTA.config.USER_PERMISSION_CONFIG = USER_PERMISSION_CONFIG;
    window.OTA.config.PERMISSION_TEMPLATES = PERMISSION_TEMPLATES;
    window.OTA.config.getUserPermissions = getUserPermissions;
    window.OTA.config.hasPermission = hasPermission;
    window.OTA.config.getAllowedChannels = getAllowedChannels;

    // 调试模式下的日志
    if (USER_PERMISSION_CONFIG.config.debugMode) {
        console.log('✅ 用户权限配置已加载', {
            version: USER_PERMISSION_CONFIG.config.version,
            restrictedUsersCount: Object.keys(USER_PERMISSION_CONFIG.restrictedUsers).length,
            templatesCount: Object.keys(PERMISSION_TEMPLATES).length
        });
    }

})();