<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化API调用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🚀 简化Gemini API调用测试</h1>
    
    <div class="test-container">
        <h3>📊 系统状态检查</h3>
        <div id="system-status">正在检查系统状态...</div>
    </div>
    
    <div class="test-container">
        <h3>🧪 API调用测试</h3>
        <p>测试简化后的API调用流程（无缓存、单次调用）</p>
        
        <textarea id="test-input" placeholder="输入订单文本进行测试...">📧 Email: <EMAIL>
📱 联系: WhatsApp +60123456789
🚗 服务类型: 接送服务
📍 出发地: Kuala Lumpur Airport (KLIA)
📍 目的地: Petaling Jaya Hotel
📅 日期: 2025-01-15
⏰ 时间: 14:30
👥 乘客: 2人
💰 价格: RM 85</textarea>
        
        <br>
        <button id="test-btn" onclick="testAPICall()">测试API调用</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="test-result" class="status info">等待测试...</div>
        <div id="test-log" class="log">日志将在这里显示...\n</div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/flow/gemini-caller.js"></script>
    
    <script>
        let testLog = document.getElementById('test-log');
        let testBtn = document.getElementById('test-btn');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            testLog.textContent += logEntry;
            testLog.scrollTop = testLog.scrollHeight;
            
            console.log(`[API Test] ${logEntry}`);
        }
        
        function clearLog() {
            testLog.textContent = '日志已清空\n';
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let status = [];
            
            // 检查关键组件是否存在
            if (typeof window.GeminiCaller !== 'undefined') {
                status.push('✅ GeminiCaller类已加载');
                
                if (window.OTA?.geminiCaller) {
                    status.push('✅ GeminiCaller实例已创建');
                    
                    // 获取API状态
                    try {
                        const apiStatus = window.OTA.geminiCaller.getAPIStatus();
                        status.push(`✅ API配置: ${apiStatus.model} v${apiStatus.version}`);
                        status.push(`✅ 超时设置: ${apiStatus.baseTimeout}ms`);
                    } catch (e) {
                        status.push('❌ 无法获取API状态');
                    }
                } else {
                    status.push('❌ GeminiCaller实例未创建');
                }
            } else {
                status.push('❌ GeminiCaller类未加载');
            }
            
            if (typeof getLogger === 'function') {
                status.push('✅ Logger可用');
            } else {
                status.push('❌ Logger不可用');
            }
            
            statusDiv.innerHTML = status.map(s => 
                `<div class="${s.startsWith('✅') ? 'status success' : 'status error'}">${s}</div>`
            ).join('');
            
            log('系统状态检查完成');
            status.forEach(s => log(s.replace('✅ ', '').replace('❌ ', '')));
        }
        
        // 测试API调用
        async function testAPICall() {
            const input = document.getElementById('test-input').value.trim();
            const resultDiv = document.getElementById('test-result');
            
            if (!input) {
                resultDiv.innerHTML = '<span class="error">请输入测试文本</span>';
                return;
            }
            
            if (!window.OTA?.geminiCaller) {
                resultDiv.innerHTML = '<span class="error">GeminiCaller未初始化</span>';
                return;
            }
            
            testBtn.disabled = true;
            resultDiv.innerHTML = '<span class="info">🤖 正在调用Gemini API...</span>';
            
            try {
                log('开始API调用测试', 'info');
                log(`输入文本长度: ${input.length}`, 'info');
                
                const startTime = performance.now();
                
                // 测试简化后的API调用
                const result = await window.OTA.geminiCaller.callAPI(input, 'text', {
                    isRealtime: false
                });
                
                const duration = performance.now() - startTime;
                
                log(`API调用成功，耗时: ${duration.toFixed(2)}ms`, 'success');
                log(`返回结果: ${JSON.stringify(result, null, 2)}`, 'success');
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ API调用成功<br>
                        ⏱️ 耗时: ${duration.toFixed(2)}ms<br>
                        📊 返回数据: ${result ? '有' : '无'}
                    </div>
                `;
                
            } catch (error) {
                const duration = performance.now() - startTime;
                
                log(`API调用失败: ${error.message}`, 'error');
                log(`失败耗时: ${duration.toFixed(2)}ms`, 'error');
                
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ API调用失败<br>
                        ⚠️ 错误: ${error.message}<br>
                        ⏱️ 耗时: ${duration.toFixed(2)}ms
                    </div>
                `;
            } finally {
                testBtn.disabled = false;
            }
        }
        
        // 页面加载完成后检查状态
        window.addEventListener('load', () => {
            log('API测试页面已加载', 'info');
            
            // 延迟检查，确保脚本加载完成
            setTimeout(() => {
                checkSystemStatus();
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error?.message || event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>