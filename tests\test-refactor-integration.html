<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单管理器重构集成测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 多订单管理器重构集成测试</h1>
        <p>验证重构后的模块化架构是否正常工作</p>

        <div class="test-section">
            <h2>📋 模块加载状态检查</h2>
            <button onclick="checkModuleStatus()">检查模块状态</button>
            <div id="moduleStatus"></div>
        </div>

        <div class="test-section">
            <h2>🔧 多订单管理器实例化测试</h2>
            <button onclick="testManagerInstantiation()">测试管理器实例化</button>
            <div id="managerTest"></div>
        </div>

        <div class="test-section">
            <h2>🔄 字段映射功能测试</h2>
            <button onclick="testFieldMapping()">测试字段映射</button>
            <div id="fieldMappingTest"></div>
        </div>

        <div class="test-section">
            <h2>🔗 向后兼容性测试</h2>
            <button onclick="testBackwardCompatibility()">测试向后兼容性</button>
            <div id="compatibilityTest"></div>
        </div>

        <div class="test-section">
            <h2>🎯 综合功能测试</h2>
            <button onclick="runComprehensiveTest()">运行综合测试</button>
            <div id="comprehensiveTest"></div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <!-- ota-registry.js removed - merged into unified OTA system -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    
    <!-- 多订单模块 -->
    <script src="js/multi-order/field-mapping-config.js"></script>
    <script src="js/multi-order/field-mapping-validator.js"></script>
    <script src="js/multi-order/multi-order-detector.js"></script>
    <script src="js/multi-order/multi-order-renderer.js"></script>
    <script src="js/multi-order/multi-order-processor.js"></script>
    <script src="js/multi-order/multi-order-transformer.js"></script>
    <script src="js/multi-order/field-mapping-tests.js"></script>
    
    <!-- 新架构多订单管理器适配器 -->
    <script src="js/adapters/multi-order-manager-adapter.js"></script>

    <script>
        // 测试函数
        function checkModuleStatus() {
            const statusDiv = document.getElementById('moduleStatus');
            const modules = {
                'FieldMappingConfig': window.OTA?.FieldMappingConfig,
                'FieldMappingValidator': window.OTA?.FieldMappingValidator,
                'MultiOrderDetector': window.OTA?.MultiOrderDetector,
                'MultiOrderRenderer': window.OTA?.MultiOrderRenderer,
                'MultiOrderProcessor': window.OTA?.MultiOrderProcessor,
                'MultiOrderTransformer': window.OTA?.MultiOrderTransformer,
                'FieldMappingTests': window.OTA?.FieldMappingTests,
                'MultiOrderManager': window.OTA?.MultiOrderManager
            };

            let html = '<h3>模块加载状态：</h3>';
            let allLoaded = true;

            for (const [name, module] of Object.entries(modules)) {
                const loaded = !!module;
                allLoaded = allLoaded && loaded;
                html += `<div class="test-result ${loaded ? 'success' : 'error'}">
                    ${loaded ? '✅' : '❌'} ${name}: ${loaded ? '已加载' : '未加载'}
                </div>`;
            }

            html += `<div class="test-result ${allLoaded ? 'success' : 'error'}">
                <strong>${allLoaded ? '✅ 所有模块加载成功' : '❌ 部分模块加载失败'}</strong>
            </div>`;

            statusDiv.innerHTML = html;
        }

        function testManagerInstantiation() {
            const testDiv = document.getElementById('managerTest');
            try {
                const manager = window.getMultiOrderManager();
                
                const moduleStatus = {
                    detector: !!manager.detector,
                    renderer: !!manager.renderer,
                    processor: !!manager.processor,
                    transformer: !!manager.transformer
                };

                let html = '<h3>管理器实例化结果：</h3>';
                html += '<div class="test-result success">✅ 多订单管理器实例化成功</div>';
                
                html += '<h4>子模块状态：</h4>';
                for (const [name, status] of Object.entries(moduleStatus)) {
                    html += `<div class="test-result ${status ? 'success' : 'error'}">
                        ${status ? '✅' : '❌'} ${name}: ${status ? '可用' : '不可用'}
                    </div>`;
                }

                testDiv.innerHTML = html;
            } catch (error) {
                testDiv.innerHTML = `<div class="test-result error">❌ 管理器实例化失败: ${error.message}</div>`;
            }
        }

        function testFieldMapping() {
            const testDiv = document.getElementById('fieldMappingTest');
            try {
                const manager = window.getMultiOrderManager();
                
                if (!manager.transformer) {
                    testDiv.innerHTML = '<div class="test-result error">❌ 数据转换器不可用</div>';
                    return;
                }

                // 测试数据转换
                const testData = {
                    customer_name: '张三',
                    pickup_location: '首都机场T3',
                    passenger_count: '2',
                    luggage_count: '3'
                };

                const transformed = manager.transformer.transformOrderData(testData);

                let html = '<h3>字段映射测试结果：</h3>';
                html += '<div class="test-result success">✅ 数据转换器功能正常</div>';
                html += '<h4>转换示例：</h4>';
                html += `<pre>原始数据: ${JSON.stringify(testData, null, 2)}</pre>`;
                html += `<pre>转换结果: ${JSON.stringify(transformed, null, 2)}</pre>`;

                testDiv.innerHTML = html;
            } catch (error) {
                testDiv.innerHTML = `<div class="test-result error">❌ 字段映射测试失败: ${error.message}</div>`;
            }
        }

        function testBackwardCompatibility() {
            const testDiv = document.getElementById('compatibilityTest');
            
            const legacyFunctions = [
                'window.multiOrderManager',
                'window.getMultiOrderManager',
                'window.OTA.multiOrderManager',
                'window.OTA.getMultiOrderManager',
                'window.MultiOrderManager'
            ];

            let html = '<h3>向后兼容性测试结果：</h3>';
            let allCompatible = true;

            for (const funcPath of legacyFunctions) {
                const parts = funcPath.split('.');
                let obj = window;
                for (let i = 1; i < parts.length; i++) {
                    obj = obj[parts[i]];
                    if (!obj) break;
                }
                
                const available = !!obj;
                allCompatible = allCompatible && available;
                
                html += `<div class="test-result ${available ? 'success' : 'error'}">
                    ${available ? '✅' : '❌'} ${funcPath}: ${available ? '可用' : '不可用'}
                </div>`;
            }

            html += `<div class="test-result ${allCompatible ? 'success' : 'error'}">
                <strong>${allCompatible ? '✅ 向后兼容性完整' : '❌ 向后兼容性不完整'}</strong>
            </div>`;

            testDiv.innerHTML = html;
        }

        function runComprehensiveTest() {
            const testDiv = document.getElementById('comprehensiveTest');
            let html = '<h3>综合测试结果：</h3>';
            let overallSuccess = true;

            try {
                // 1. 模块加载检查
                const modules = ['FieldMappingConfig', 'FieldMappingValidator', 'MultiOrderDetector', 
                               'MultiOrderRenderer', 'MultiOrderProcessor', 'MultiOrderTransformer', 
                               'FieldMappingTests', 'MultiOrderManager'];
                
                const moduleCheck = modules.every(name => !!window.OTA?.[name]);
                html += `<div class="test-result ${moduleCheck ? 'success' : 'error'}">
                    ${moduleCheck ? '✅' : '❌'} 模块加载: ${moduleCheck ? '通过' : '失败'}
                </div>`;
                overallSuccess = overallSuccess && moduleCheck;

                // 2. 管理器实例化检查
                const manager = window.getMultiOrderManager();
                const managerCheck = !!(manager && manager.detector && manager.renderer && 
                                       manager.processor && manager.transformer);
                html += `<div class="test-result ${managerCheck ? 'success' : 'error'}">
                    ${managerCheck ? '✅' : '❌'} 管理器协调: ${managerCheck ? '通过' : '失败'}
                </div>`;
                overallSuccess = overallSuccess && managerCheck;

                // 3. 字段映射功能检查
                const testData = { customer_name: '测试', pickup_location: '测试地点' };
                const transformed = manager.transformer.transformOrderData(testData);
                const mappingCheck = !!(transformed && typeof transformed === 'object');
                html += `<div class="test-result ${mappingCheck ? 'success' : 'error'}">
                    ${mappingCheck ? '✅' : '❌'} 字段映射: ${mappingCheck ? '通过' : '失败'}
                </div>`;
                overallSuccess = overallSuccess && mappingCheck;

                // 4. 向后兼容性检查
                const compatibilityCheck = !!(window.multiOrderManager && window.getMultiOrderManager);
                html += `<div class="test-result ${compatibilityCheck ? 'success' : 'error'}">
                    ${compatibilityCheck ? '✅' : '❌'} 向后兼容: ${compatibilityCheck ? '通过' : '失败'}
                </div>`;
                overallSuccess = overallSuccess && compatibilityCheck;

                // 总结
                html += `<div class="test-result ${overallSuccess ? 'success' : 'error'}">
                    <strong>${overallSuccess ? '🎉 重构集成测试全部通过！' : '❌ 重构集成测试部分失败'}</strong>
                </div>`;

            } catch (error) {
                html += `<div class="test-result error">❌ 综合测试执行失败: ${error.message}</div>`;
                overallSuccess = false;
            }

            testDiv.innerHTML = html;
        }

        // 页面加载完成后自动运行基础检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkModuleStatus();
                console.log('🧪 重构集成测试页面已加载');
            }, 1000);
        });
    </script>
</body>
</html>
