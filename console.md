---
type: "manual"
---

script-manifest.js:134 ✅ Script manifest ready
script-loader.js:164 ✅ ScriptLoader ready
script-loader.js:121 🔧 Loading phase: core (13 scripts)
dependency-container.js:298 ✅ 依赖容器已初始化
service-locator.js:90 ✅ 服务定位器已初始化
service-locator.js:420 ✅ 服务定位器已加载
application-bootstrap.js:505 ✅ 应用启动协调器已加载
logger.js:472 [3:37:41 AM] [INFO] 🔍 初始化基础监控系统... {type: 'monitoring_init'}
logger.js:472 [3:37:41 AM] [INFO] 全局错误处理已设置 {type: 'global_error_handler_init'}
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 基础监控系统初始化完成 {type: 'monitoring_ready'}
logger.js:472 [3:37:41 AM] [INFO] OTA订单处理系统启动 {type: 'system_start', userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb…KHTML, like Gecko) Chrome/********* Safari/537.36', timestamp: '2025-08-11T19:37:41.048Z'}
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: eventCoordinator 
logger.js:198 [DependencyContainer] 已注册服务: eventCoordinator
logger.js:472 [3:37:41 AM] [INFO] [EventCoordinator] 已注册组件: vehicleConfigManager {options: {…}}
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: vehicleConfigManager 
logger.js:198 [DependencyContainer] 已注册服务: vehicleConfigManager
logger.js:472 [3:37:41 AM] [DEBUG] [VehicleConfigManager] 已注册到依赖容器 
logger.js:472 [3:37:41 AM] [INFO] [VehicleConfigManager] 车辆配置管理器已初始化 {defaultCarTypeId: 5, recommendationRules: 8}
logger.js:472 [3:37:41 AM] [INFO] ✅ 车辆配置管理器已加载 
logger.js:198 ✅ 车辆配置管理器已加载
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] ✅ 全局字段标准化拦截层已加载并设置自动初始化 
logger.js:198 [GlobalFieldStandardization] ✅ 全局字段标准化拦截层已加载并设置自动初始化
logger.js:472 [3:37:41 AM] [INFO] 特性开关管理器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ 特性开关机制已加载 
logger.js:198 ✅ 特性开关机制已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ 完整酒店数据模块已加载 
logger.js:198 ✅ 完整酒店数据模块已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ 完整酒店数据初始化完成，共 87 家酒店 
logger.js:198 ✅ 完整酒店数据初始化完成，共 87 家酒店
logger.js:472 [3:37:41 AM] [INFO] 📊 数据统计: 87 家酒店，15 个区域 
logger.js:198 📊 数据统计: 87 家酒店，15 个区域
logger.js:472 [3:37:41 AM] [INFO] ✅ Phase complete: core in 114.6ms 
logger.js:198 ✅ Phase complete: core in 114.6ms
logger.js:472 [3:37:41 AM] [INFO] 🔧 Loading phase: ota-architecture (18 scripts) 
logger.js:198 🔧 Loading phase: ota-architecture (18 scripts)
logger.js:472 [3:37:41 AM] [INFO] ✅ BaseManager适配器已加载，BaseManager全局类已可用 
logger.js:198 ✅ BaseManager适配器已加载，BaseManager全局类已可用
logger.js:472 [3:37:41 AM] [INFO] ✅ 统一版OTA管理器已加载 
logger.js:198 ✅ 统一版OTA管理器已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ 统一OTA策略配置已加载 
logger.js:198 ✅ 统一OTA策略配置已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ ChannelDetector (子层实现) 已加载 
logger.js:198 ✅ ChannelDetector (子层实现) 已加载
logger.js:472 [3:37:41 AM] [INFO] 提示词构建器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ PromptBuilder (子层实现) 已加载 
logger.js:198 ✅ PromptBuilder (子层实现) 已加载
logger.js:472 [3:37:41 AM] [INFO] Gemini API调用器已初始化 {model: 'gemini-2.5-flash'}
logger.js:472 [3:37:41 AM] [INFO] ✅ GeminiCaller (子层实现) 已加载 
logger.js:198 ✅ GeminiCaller (子层实现) 已加载
logger.js:472 [3:37:41 AM] [INFO] 结果处理器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ ResultProcessor (子层实现) 已加载 
logger.js:198 ✅ ResultProcessor (子层实现) 已加载
logger.js:472 [3:37:41 AM] [INFO] 订单解析器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ OrderParser (子层实现) 已加载 
logger.js:198 ✅ OrderParser (子层实现) 已加载
logger.js:472 [3:37:41 AM] [INFO] 开始初始化知识库... 
logger.js:472 [3:37:41 AM] [INFO] 开始加载酒店知识库... 
logger.js:469 [3:37:41 AM] [SUCCESS] 酒店数据处理完成 {totalHotels: 87, mappings: 87}
logger.js:469 [3:37:41 AM] [SUCCESS] 使用完整内联酒店数据 {totalHotels: 87, source: 'complete_inline_data'}
logger.js:469 [3:37:41 AM] [SUCCESS] 机场数据加载完成 {totalAirports: 3}
logger.js:472 [3:37:41 AM] [INFO] 知识库管理器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ KnowledgeBase (子层实现) 已加载 
logger.js:198 ✅ KnowledgeBase (子层实现) 已加载
logger.js:469 [3:37:41 AM] [SUCCESS] 知识库初始化完成 {hotels: 87, airports: 3}
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] 🔧 特性开关已启用，开始自动初始化... 
logger.js:198 [GlobalFieldStandardization] 🔧 特性开关已启用，开始自动初始化...
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) [object Object] 
logger.js:198 [GlobalFieldStandardization] 尝试安装Gemini拦截器 (1/150) {hasAdapter: false, hasCaller: true, adapterHasParseOrder: false, adapterHasLegacyParse: false, callerHasParseAPIResponse: true}
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] ✅ GeminiCaller拦截器已安装 (parseAPIResponse) 
logger.js:198 [GlobalFieldStandardization] ✅ GeminiCaller拦截器已安装 (parseAPIResponse)
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截 
logger.js:198 [GlobalFieldStandardization] ⚠️ 表单管理器服务未在依赖容器中注册，跳过拦截
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] ℹ️ 跳过实时分析管理器拦截器（减法修复） 
logger.js:198 [GlobalFieldStandardization] ℹ️ 跳过实时分析管理器拦截器（减法修复）
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] ✅ 字段标准化拦截层初始化完成 
logger.js:198 [GlobalFieldStandardization] ✅ 字段标准化拦截层初始化完成
logger.js:472 [3:37:41 AM] [INFO] 🌐 开始初始化统一语言检测器... 
logger.js:198 🌐 开始初始化统一语言检测器...
logger.js:472 [3:37:41 AM] [INFO] 🌐 初始化统一语言检测器... 
logger.js:472 [3:37:41 AM] [INFO] ✅ 已绑定字段事件: #customerName 
logger.js:472 [3:37:41 AM] [INFO] ✅ 已绑定字段事件: #pickup 
logger.js:472 [3:37:41 AM] [INFO] ✅ 已绑定字段事件: #dropoff 
logger.js:472 [3:37:41 AM] [INFO] ✅ 已绑定字段事件: #extraRequirement 
logger.js:472 [3:37:41 AM] [INFO] ✅ 已绑定字段事件: #flightInfo 
logger.js:472 [3:37:41 AM] [INFO] 📝 已绑定 5 个字段的语言检测事件 
logger.js:472 [3:37:41 AM] [INFO] ✅ 已设置默认语言（英文） 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 统一语言检测器初始化完成 
logger.js:472 [3:37:41 AM] [INFO] ✅ 统一语言检测器初始化成功 
logger.js:198 ✅ 统一语言检测器初始化成功
logger.js:472 [3:37:41 AM] [INFO] 地址翻译器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ AddressTranslator (子层实现) 已加载 
logger.js:198 ✅ AddressTranslator (子层实现) 已加载
logger.js:472 [3:37:41 AM] [INFO] 多订单处理器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ MultiOrderHandler (子层实现) 已加载 
logger.js:198 ✅ MultiOrderHandler (子层实现) 已加载
logger.js:472 [3:37:41 AM] [INFO] API调用器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ APICaller (子层实现) 已加载 
logger.js:198 ✅ APICaller (子层实现) 已加载
logger.js:469 [3:37:41 AM] [SUCCESS] 历史数据加载成功 {}
logger.js:469 [3:37:41 AM] [SUCCESS] 历史管理器初始化完成 {historyCount: 0}
logger.js:472 [3:37:41 AM] [INFO] 历史管理器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ HistoryManager (子层实现) 已加载 
logger.js:198 ✅ HistoryManager (子层实现) 已加载
logger.js:472 [3:37:41 AM] [INFO] 渠道检测器已初始化 
logger.js:472 [3:37:41 AM] [INFO] 业务流程控制器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ BusinessFlowController (母层控制器) 已加载 
logger.js:198 ✅ BusinessFlowController (母层控制器) 已加载
logger.js:472 [3:37:41 AM] [INFO] 订单管理控制器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ OrderManagementController (母层控制器) 已加载 
logger.js:198 ✅ OrderManagementController (母层控制器) 已加载
logger.js:472 [3:37:41 AM] [INFO] Gemini服务适配器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✅ GeminiServiceAdapter (兼容性适配器) 已加载 
logger.js:198 ✅ GeminiServiceAdapter (兼容性适配器) 已加载
logger.js:472 [3:37:41 AM] [INFO] 🔄 MultiOrderManagerAdapter 初始化开始 
logger.js:472 [3:37:41 AM] [INFO] ✅ MultiOrderManagerAdapter 已加载并注册 
logger.js:198 ✅ MultiOrderManagerAdapter 已加载并注册
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ MultiOrderManagerAdapter 初始化完成 
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] ✅ 多订单管理器拦截器已安装 
logger.js:198 [GlobalFieldStandardization] ✅ 多订单管理器拦截器已安装
logger.js:472 [3:37:41 AM] [INFO] 🔄 UIManagerAdapter 初始化开始 
logger.js:472 [3:37:41 AM] [INFO] ✅ UIManagerAdapter 已加载并注册 
logger.js:198 ✅ UIManagerAdapter 已加载并注册
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ UIManagerAdapter 初始化完成 
logger.js:472 [3:37:41 AM] [INFO] ✅ Phase complete: ota-architecture in 183.1ms 
logger.js:198 ✅ Phase complete: ota-architecture in 183.1ms
logger.js:472 [3:37:41 AM] [INFO] 🔧 Loading phase: services (18 scripts) 
logger.js:198 🔧 Loading phase: services (18 scripts)
logger.js:466 [3:37:41 AM] [WARNING] 部分子层模块未加载，将使用降级方案 
outputToConsole @ logger.js:466
log @ logger.js:383
(anonymous) @ order-management-controller.js:96
setTimeout
initializeChildLayers @ order-management-controller.js:89
OrderManagementController @ order-management-controller.js:78
(anonymous) @ order-management-controller.js:330
(anonymous) @ order-management-controller.js:343
logger.js:469 [3:37:41 AM] [SUCCESS] 新架构组件连接成功 
logger.js:472 [3:37:41 AM] [INFO] ✅ 内联酒店数据模块已加载 
logger.js:198 ✅ 内联酒店数据模块已加载
logger.js:472 [3:37:41 AM] [INFO] [GlobalFieldStandardization] ✅ API服务拦截器已安装 
logger.js:198 [GlobalFieldStandardization] ✅ API服务拦截器已安装
logger.js:472 [3:37:41 AM] [INFO] 历史订单关闭按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单清空按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单导出按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单搜索按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单重置按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单管理器已初始化（按账号存储） 
logger.js:469 [3:37:41 AM] [SUCCESS] 图片上传按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 图片上传管理器已初始化 
logger.js:472 [3:37:41 AM] [INFO] ✈️ 航班信息服务已初始化 {baseUrl: '', timestamp: '2025-08-11T19:37:41.486Z'}
logger.js:472 [3:37:41 AM] [INFO] ✅ 字段映射配置已加载 [object Object] 
logger.js:198 ✅ 字段映射配置已加载 {aiToFrontendMappings: 22, frontendToApiMappings: 28, alternativeFields: 14}
logger.js:472 [3:37:41 AM] [INFO] ✅ 字段映射验证器已加载 
logger.js:198 ✅ 字段映射验证器已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ 多订单检测服务已加载 
logger.js:198 ✅ 多订单检测服务已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ 多订单UI渲染器已加载 
logger.js:198 ✅ 多订单UI渲染器已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ 多订单批量处理器已加载 
logger.js:198 ✅ 多订单批量处理器已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ 多订单数据转换器已加载 
logger.js:198 ✅ 多订单数据转换器已加载
logger.js:472 [3:37:41 AM] [INFO] 状态已从本地存储加载 
logger.js:472 [3:37:41 AM] [INFO] 多订单状态管理器初始化完成 
logger.js:472 [3:37:41 AM] [INFO] 多订单协调器模块初始化完成 
logger.js:472 [3:37:41 AM] [INFO] ✅ 系统完整性检查器已加载 
logger.js:198 ✅ 系统完整性检查器已加载
logger.js:472 [3:37:41 AM] [INFO] ✅ Phase complete: services in 243.5ms 
logger.js:198 ✅ Phase complete: services in 243.5ms
logger.js:472 [3:37:41 AM] [INFO] 🔧 Loading phase: ui-managers (8 scripts) 
logger.js:198 🔧 Loading phase: ui-managers (8 scripts)
logger.js:472 [3:37:41 AM] [INFO] 🔧 自适应高度管理器初始化中... 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: extraRequirement 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: pickup 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: dropoff 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: customerName 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: flightInfo 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: otaReferenceNumber 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: orderInput 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: searchOrderId 
logger.js:472 [3:37:41 AM] [INFO] 📏 已添加自适应高度管理: searchCustomer 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 自适应高度管理器初始化完成 
logger.js:472 [3:37:41 AM] [INFO] 🎬 初始化动画管理器... 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 按钮动画处理器初始化完成 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 动画管理器初始化完成 {enabled: true, config: {…}}
logger.js:472 [3:37:41 AM] [INFO] ✅ 动画管理器已加载并初始化 
logger.js:198 ✅ 动画管理器已加载并初始化
logger.js:472 [3:37:41 AM] [INFO] ✅ Phase complete: ui-managers in 85.9ms 
logger.js:198 ✅ Phase complete: ui-managers in 85.9ms
logger.js:472 [3:37:41 AM] [INFO] 🔧 Loading phase: ui (2 scripts) 
logger.js:198 🔧 Loading phase: ui (2 scripts)
logger.js:472 [3:37:41 AM] [INFO] 🚀 开始启动OTA订单处理系统... 
logger.js:198 🚀 开始启动OTA订单处理系统...
logger.js:472 [3:37:41 AM] [INFO] 🚀 开始启动OTA订单处理系统... 
logger.js:198 🚀 开始启动OTA订单处理系统...
logger.js:472 [3:37:41 AM] [INFO] 📋 执行启动阶段: dependencies (1/5) 
logger.js:198 📋 执行启动阶段: dependencies (1/5)
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: appState 
logger.js:198 [DependencyContainer] 已注册服务: appState
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: logger 
logger.js:198 [DependencyContainer] 已注册服务: logger
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: utils 
logger.js:198 [DependencyContainer] 已注册服务: utils
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 警告: 服务 eventCoordinator 已存在，将被覆盖 
logger.js:198 [DependencyContainer] 警告: 服务 eventCoordinator 已存在，将被覆盖
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: eventCoordinator 
logger.js:198 [DependencyContainer] 已注册服务: eventCoordinator
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: apiService 
logger.js:198 [DependencyContainer] 已注册服务: apiService
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: geminiService 
logger.js:198 [DependencyContainer] 已注册服务: geminiService
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: i18nManager 
logger.js:198 [DependencyContainer] 已注册服务: i18nManager
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: imageUploadManager 
logger.js:198 [DependencyContainer] 已注册服务: imageUploadManager
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: currencyConverter 
logger.js:198 [DependencyContainer] 已注册服务: currencyConverter
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: multiOrderManager 
logger.js:198 [DependencyContainer] 已注册服务: multiOrderManager
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: orderHistoryManager 
logger.js:198 [DependencyContainer] 已注册服务: orderHistoryManager
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: pagingServiceManager 
logger.js:198 [DependencyContainer] 已注册服务: pagingServiceManager
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: uiManager 
logger.js:198 [DependencyContainer] 已注册服务: uiManager
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已注册服务: channelDetector 
logger.js:198 [DependencyContainer] 已注册服务: channelDetector
logger.js:472 [3:37:41 AM] [INFO] 📦 已注册 14 个依赖 
logger.js:198 📦 已注册 14 个依赖
logger.js:472 [3:37:41 AM] [INFO] 📋 执行启动阶段: services (2/5) 
logger.js:198 📋 执行启动阶段: services (2/5)
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: appState 
logger.js:198 [DependencyContainer] 已创建服务实例: appState
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: logger 
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: utils 
logger.js:472 [3:37:41 AM] [INFO] [EventCoordinator] 全局事件监听器已设置 
logger.js:472 [3:37:41 AM] [INFO] [EventCoordinator] 全局事件协调器已初始化 
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: eventCoordinator 
logger.js:466 [3:37:41 AM] [WARNING] [EventCoordinator] 全局事件协调器已经初始化 
outputToConsole @ logger.js:466
log @ logger.js:383
log @ global-event-coordinator.js:331
init @ global-event-coordinator.js:29
initializeServices @ application-bootstrap.js:256
executePhase @ application-bootstrap.js:113
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: apiService 
logger.js:472 [3:37:41 AM] [INFO] [ApiService] 开始初始化... 
logger.js:472 [3:37:41 AM] [INFO] [ApiService] AppState中已有系统数据，跳过初始化 
logger.js:469 [3:37:41 AM] [SUCCESS] [ApiService] 初始化完成 
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: geminiService 
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: channelDetector 
logger.js:472 [3:37:41 AM] [INFO] ⚙️ 已初始化 6 个核心服务 
logger.js:198 ⚙️ 已初始化 6 个核心服务
logger.js:472 [3:37:41 AM] [INFO] 📋 执行启动阶段: managers (3/5) 
logger.js:198 📋 执行启动阶段: managers (3/5)
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: imageUploadManager 
logger.js:469 [3:37:41 AM] [SUCCESS] 图片上传按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 图片上传管理器已初始化 
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: currencyConverter 
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: multiOrderManager 
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: orderHistoryManager 
logger.js:472 [3:37:41 AM] [INFO] 历史订单关闭按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单清空按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单导出按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单搜索按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单重置按钮事件已绑定 
logger.js:472 [3:37:41 AM] [INFO] 历史订单管理器已初始化（按账号存储） 
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: pagingServiceManager 
logger.js:472 [3:37:41 AM] [INFO] 🎛️ 已处理 5 个管理器 
logger.js:198 🎛️ 已处理 5 个管理器
logger.js:472 [3:37:41 AM] [INFO] 📋 执行启动阶段: ui (4/5) 
logger.js:198 📋 执行启动阶段: ui (4/5)
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: i18nManager 
logger.js:472 [3:37:41 AM] [INFO] 国际化管理器已初始化，当前语言: en 
logger.js:472 [3:37:41 AM] [INFO] ✅ Phase complete: ui in 79.0ms 
logger.js:198 ✅ Phase complete: ui in 79.0ms
logger.js:472 [3:37:41 AM] [INFO] 🚀 All scripts loaded in 707.3ms 
logger.js:198 🚀 All scripts loaded in 707.3ms
logger.js:472 [3:37:41 AM] [INFO] [DependencyContainer] 已创建服务实例: uiManager 
logger.js:472 [3:37:41 AM] [INFO] UIManager 开始初始化... 
logger.js:472 [3:37:41 AM] [INFO] DOM元素缓存完成 
logger.js:472 [3:37:41 AM] [INFO] DOM元素已缓存 
logger.js:472 [3:37:41 AM] [INFO] 显示主工作区 
logger.js:472 [3:37:41 AM] [INFO] 系统数据已存在，无需重新初始化 {languagesCount: 11}
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 表单管理器动画集成成功 
logger.js:472 [3:37:41 AM] [INFO] 价格转换监听器初始化完成（货币切换逻辑由PriceManager处理） 
logger.js:469 [3:37:41 AM] [SUCCESS] 自适应高度输入框初始化完成 
logger.js:469 [3:37:41 AM] [SUCCESS] 表单管理器初始化完成 
logger.js:469 [3:37:41 AM] [SUCCESS] 价格管理器初始化完成 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ UI状态管理器动画集成成功 
logger.js:472 [3:37:41 AM] [INFO] 状态监听器设置完成 
logger.js:472 [3:37:41 AM] [INFO] UI已切换到工作区 
logger.js:469 [3:37:41 AM] [SUCCESS] 状态管理器初始化完成 
logger.js:472 [3:37:41 AM] [INFO] 开始绑定事件监听器... 
logger.js:469 [3:37:41 AM] [SUCCESS] 所有事件监听器绑定完成 
logger.js:469 [3:37:41 AM] [SUCCESS] 事件管理器初始化完成 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 实时分析管理器动画集成成功 
logger.js:472 [3:37:41 AM] [INFO] 实时分析功能已配置（性能优化版） {debounceDelay: 500, minInputLength: 10}
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 订单输入框事件绑定成功 
logger.js:472 [3:37:41 AM] [INFO] 🌐 使用统一语言检测器，跳过重复绑定 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 实时分析事件绑定验证完成 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 实时分析管理器初始化完成 
logger.js:469 [3:37:41 AM] [SUCCESS] 管理器模块初始化完成 
logger.js:469 [3:37:41 AM] [SUCCESS] 图片上传按钮事件已重新绑定 
logger.js:469 [3:37:41 AM] [SUCCESS] UI管理器初始化完成 
logger.js:472 [3:37:41 AM] [INFO] 🎨 用户界面初始化完成 
logger.js:198 🎨 用户界面初始化完成
logger.js:472 [3:37:41 AM] [INFO] 📋 执行启动阶段: finalization (5/5) 
logger.js:198 📋 执行启动阶段: finalization (5/5)
logger.js:472 [3:37:41 AM] [INFO] [ApplicationBootstrap] 字段标准化层状态检查 [object Object] 
logger.js:198 [ApplicationBootstrap] 字段标准化层状态检查 {layerExists: true, featureEnabled: true, initialized: true}
logger.js:472 [3:37:41 AM] [INFO] [ApplicationBootstrap] ✅ 字段标准化层已初始化 
logger.js:198 [ApplicationBootstrap] ✅ 字段标准化层已初始化
logger.js:472 [3:37:41 AM] [INFO] 🏁 系统启动完成 
logger.js:198 🏁 系统启动完成
logger.js:472 [3:37:41 AM] [INFO] ✅ OTA系统启动完成，总耗时: 169.50ms 
logger.js:198 ✅ OTA系统启动完成，总耗时: 169.50ms
application-bootstrap.js:469 📊 启动报告
logger.js:472 [3:37:41 AM] [INFO] ✅ dependencies: 2.10ms 
logger.js:198 ✅ dependencies: 2.10ms
logger.js:472 [3:37:41 AM] [INFO]    详情: 已注册: appState, 已注册: logger, 已注册: utils, 已注册: eventCoordinator, 已注册: apiService, 已注册: geminiService, 已注册: i18nManager, 已注册: imageUploadManager, 已注册: currencyConverter, 已注册: multiOrderManager, 已注册: orderHistoryManager, 已注册: pagingServiceManager, 已注册: uiManager, 已注册: channelDetector 
logger.js:198    详情: 已注册: appState, 已注册: logger, 已注册: utils, 已注册: eventCoordinator, 已注册: apiService, 已注册: geminiService, 已注册: i18nManager, 已注册: imageUploadManager, 已注册: currencyConverter, 已注册: multiOrderManager, 已注册: orderHistoryManager, 已注册: pagingServiceManager, 已注册: uiManager, 已注册: channelDetector
logger.js:472 [3:37:41 AM] [INFO] ✅ services: 25.40ms 
logger.js:198 ✅ services: 25.40ms
logger.js:472 [3:37:41 AM] [INFO]    详情: 已初始化: appState, 已初始化: logger, 已初始化: utils, 已初始化: eventCoordinator, 已初始化: apiService, 已初始化: geminiService 
logger.js:198    详情: 已初始化: appState, 已初始化: logger, 已初始化: utils, 已初始化: eventCoordinator, 已初始化: apiService, 已初始化: geminiService
logger.js:472 [3:37:41 AM] [INFO] ✅ managers: 29.30ms 
logger.js:198 ✅ managers: 29.30ms
logger.js:472 [3:37:41 AM] [INFO]    详情: 已初始化: imageUploadManager, 已初始化: currencyConverter, 已初始化: multiOrderManager, 已初始化: orderHistoryManager, 已初始化: pagingServiceManager 
logger.js:198    详情: 已初始化: imageUploadManager, 已初始化: currencyConverter, 已初始化: multiOrderManager, 已初始化: orderHistoryManager, 已初始化: pagingServiceManager
logger.js:472 [3:37:41 AM] [INFO] ✅ ui: 111.00ms 
logger.js:198 ✅ ui: 111.00ms
logger.js:472 [3:37:41 AM] [INFO]    详情: 国际化管理器已初始化, UI管理器已初始化 
logger.js:198    详情: 国际化管理器已初始化, UI管理器已初始化
logger.js:472 [3:37:41 AM] [INFO] ✅ finalization: 0.80ms 
logger.js:198 ✅ finalization: 0.80ms
logger.js:472 [3:37:41 AM] [INFO]    详情: 健康检查: 90/100, 全局错误处理已设置, 调试接口已暴露, 字段标准化层初始化完成 
logger.js:198    详情: 健康检查: 90/100, 全局错误处理已设置, 调试接口已暴露, 字段标准化层初始化完成
logger.js:472 [3:37:41 AM] [INFO] ✅ OTA系统启动成功，耗时: 169.50ms 
logger.js:198 ✅ OTA系统启动成功，耗时: 169.50ms
logger.js:472 [3:37:41 AM] [INFO] 通过邮箱匹配OTA配置 {email: '<EMAIL>', hasConfig: false}
logger.js:472 [3:37:41 AM] [INFO] 未找到用户专属OTA配置，使用通用配置 {email: '<EMAIL>'}
logger.js:472 [3:37:41 AM] [INFO] 渠道智能检测已启用 {channelCount: 133}
logger.js:472 [3:37:41 AM] [INFO] 自动分析已启用 
logger.js:472 [3:37:41 AM] [INFO] 已启用智能渠道检测和自动分析 {channelCount: 133, autoAnalysisEnabled: true}
logger.js:472 [3:37:41 AM] [INFO] 语言切换后重新填充下拉菜单选项 
logger.js:472 [3:37:41 AM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:472 [3:37:41 AM] [INFO] 通过邮箱匹配OTA配置 {email: '<EMAIL>', hasConfig: false}
logger.js:472 [3:37:41 AM] [INFO] 未找到用户专属OTA配置，使用通用配置 {email: '<EMAIL>'}
logger.js:472 [3:37:41 AM] [INFO] 渠道智能检测已启用 {channelCount: 133}
logger.js:472 [3:37:41 AM] [INFO] 自动分析已启用 
logger.js:472 [3:37:41 AM] [INFO] 已启用智能渠道检测和自动分析 {channelCount: 133, autoAnalysisEnabled: true}
logger.js:469 [3:37:41 AM] [SUCCESS] 表单选项填充完成 
logger.js:469 [3:37:41 AM] [SUCCESS] 表单选项填充完成 {subCategoriesCount: 3, carTypesCount: 18, backendUsersCount: 43, drivingRegionsCount: 11, elementsStatus: {…}}
logger.js:472 [3:37:41 AM] [INFO] 🔧 [FormManager] 初始化原生语言选择器 
logger.js:466 [3:37:41 AM] [WARNING] ⚠️ [FormManager] 语言选择器元素不存在，跳过初始化 {availableElements: Array(2)}
outputToConsole @ logger.js:466
log @ logger.js:383
initLanguagesDropdown @ form-manager.js:232
initCustomComponents @ form-manager.js:85
(anonymous) @ form-manager.js:43
setTimeout
init @ form-manager.js:40
initializeManagers @ ui-manager.js:264
init @ ui-manager.js:113
initializeUI @ application-bootstrap.js:321
await in initializeUI
executePhase @ application-bootstrap.js:119
start @ application-bootstrap.js:71
await in start
startApp @ main.js:77
(anonymous) @ main.js:123
logger.js:472 [3:37:41 AM] [INFO] 检测到用户已登录，应用权限控制 {email: '<EMAIL>'}
logger.js:472 [3:37:41 AM] [INFO] 价格字段权限检查 {email: '<EMAIL>', permissions: {…}}
logger.js:472 [3:37:41 AM] [INFO] 🔐 开始应用价格字段权限控制 {canViewOtaPrice: true, canViewDriverFee: true}
logger.js:472 [3:37:41 AM] [INFO] 💰 价格信息面板显示状态: block 
logger.js:472 [3:37:41 AM] [INFO] OTA价格字段显示状态: flex 
logger.js:472 [3:37:41 AM] [INFO] 司机费用字段显示状态: flex 
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ 价格字段权限控制应用完成 {canViewOtaPrice: true, canViewDriverFee: true, hasAnyPricePermission: true, priceInfoPanelVisible: true}
logger.js:472 [3:37:41 AM] [INFO] 语言选项权限检查 {email: '<EMAIL>', permissions: {…}}
logger.js:472 [3:37:41 AM] [INFO] 🔐 开始应用语言选项权限控制 {canUsePaging: true}
logger.js:469 [3:37:41 AM] [SUCCESS] ✅ Paging选项已启用（显示） 
logger.js:469 [3:37:41 AM] [SUCCESS] 语言选项权限控制初始化完成 {canUsePaging: true}
form-manager.js:40 [Violation] 'setTimeout' handler took 56ms
logger.js:472 [3:37:42 AM] [INFO] 根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 310}
logger.js:472 [3:37:42 AM] [INFO] 已设置默认负责人 {userId: 310}
logger.js:472 [3:37:43 AM] [INFO] 🔍 开始系统完整性检查 
logger.js:472 [3:37:43 AM] [INFO] 🔗 检查依赖关系... 
logger.js:472 [3:37:43 AM] [INFO] 📋 检查服务注册... 
logger.js:472 [3:37:43 AM] [INFO] 🎭 检查事件系统... 
logger.js:472 [3:37:43 AM] [INFO] 🚀 检查初始化逻辑... 
logger.js:472 [3:37:43 AM] [INFO] 🔄 检查向后兼容性... 
logger.js:472 [3:37:43 AM] [INFO] 🏁 系统完整性检查完成 {overall: 'ISSUES_FOUND', summary: {…}, categories: {…}, failedTests: Array(4), results: Array(25)}
logger.js:472 [3:37:43 AM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: 'unknown', textLength: 243, hasChinese: true, languageIds: Array(1)}
logger.js:466 [3:37:43 AM] [WARNING] OTA渠道下拉框未找到 {searchedSelectors: Array(5)}
outputToConsole @ logger.js:466
log @ logger.js:383
log @ language-detector.js:107
setOTAChannelSelection @ language-detector.js:672
detectAndApplyChannelOnly @ language-detector.js:465
await in detectAndApplyChannelOnly
detectAndApply @ language-detector.js:306
await in detectAndApply
detectAndSetChineseLanguage @ realtime-analysis-manager.js:820
handleRealtimeInput @ realtime-analysis-manager.js:391
logger.js:472 [3:37:43 AM] [INFO] 渠道检测结果已存储到AppState {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern', matchedPattern: '订单编号[：:\\s]*\\d{19}', sourceField: 'unknown', …}
logger.js:472 [3:37:43 AM] [INFO] 🚀 渠道检测完成并已存储结果 {channel: 'Fliggy', confidence: 0.95, sourceField: 'unknown', stored: true}
logger.js:472 [3:37:43 AM] [INFO] ⚠️ 检测到重复输入调用，跳过处理 {timeDiff: 93, isPasteEvent: true}
logger.js:472 [3:37:44 AM] [INFO] [GlobalFieldStandardization] ℹ️ 自动初始化检查已停止 
logger.js:198 [GlobalFieldStandardization] ℹ️ 自动初始化检查已停止
realtime-analysis-manager.js:415 🔍 多订单数据流追踪 - 第1步：实时分析触发
logger.js:472 [3:37:44 AM] [INFO] 输入文本长度: 243 
logger.js:198 输入文本长度: 243
logger.js:472 [3:37:44 AM] [INFO] 输入文本预览: 订单编号：2872865460249204057买家：山转水转1支付时间：2025-08-11 08:51:26
查看详情

经济7座

【送机】

新加坡-新加坡

[出发]新加坡市中豪亚酒店

[抵达]樟宜机场T1

约26.4公里

2025-08-11 14:10:00

联系人许丽俊

真实号：13916811351

---
4成人0儿童

司机姓名：kk

司机电话：17605088... 
logger.js:198 输入文本预览: 订单编号：2872865460249204057买家：山转水转1支付时间：2025-08-11 08:51:26
查看详情

经济7座

【送机】

新加坡-新加坡

[出发]新加坡市中豪亚酒店

[抵达]樟宜机场T1

约26.4公里

2025-08-11 14:10:00

联系人许丽俊

真实号：13916811351

---
4成人0儿童

司机姓名：kk

司机电话：17605088...
logger.js:472 [3:37:44 AM] [INFO] 🔍 获取到的渠道检测结果: [object Object] 
logger.js:198 🔍 获取到的渠道检测结果: {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern', matchedPattern: '订单编号[：:\\s]*\\d{19}', sourceField: 'unknown', …}
realtime-analysis-manager.js:438 🔍 多订单数据流追踪 - 第2步：调用Gemini解析
logger.js:472 [3:37:44 AM] [INFO] 🔄 开始实时订单解析... 
logger.js:472 [3:37:44 AM] [INFO] 适配器：解析订单文本 {textLength: 243, isRealtime: true, hasChannelInfo: true}
logger.js:472 [3:37:44 AM] [INFO] 开始处理输入 {type: 'text', inputLength: 243, autoTriggered: false, sourceField: 'unknown'}
logger.js:472 [3:37:44 AM] [INFO] 🚀 使用预检测的渠道结果 {channel: 'Fliggy', confidence: 0.95, method: 'fliggy_pattern'}
logger.js:472 [3:37:44 AM] [DEBUG] 🔧 [调试] 开始构建提示词 {channel: 'Fliggy', confidence: 0.95, hasStrategy: true}
logger.js:472 [3:37:44 AM] [INFO] 开始构建提示词 {channel: 'Fliggy', inputLength: 243, autoTriggered: false, sourceField: 'unknown'}
logger.js:472 [3:37:44 AM] [DEBUG] 🔧 [调试] 获取渠道策略 {channel: 'Fliggy'}
logger.js:472 [3:37:44 AM] [DEBUG] 🔧 [调试] 渠道策略找到，获取字段片段 {strategyType: 'FliggyOTAStrategy', hasGetFieldPromptSnippets: true}
logger.js:469 [3:37:44 AM] [SUCCESS] 成功获取字段提示词片段 {snippetCount: 7}
logger.js:472 [3:37:44 AM] [DEBUG] 🔧 [调试] 字段片段获取完成 {snippetCount: 7, snippetKeys: '[FILTERED]'}
logger.js:469 [3:37:44 AM] [SUCCESS] 提示词构建完成 {channel: 'Fliggy', promptLength: 5407, autoTriggered: false}
logger.js:472 [3:37:44 AM] [DEBUG] 🔧 [调试] 提示词构建完成 {promptLength: 5407, containsChannelInfo: true}
logger.js:472 [3:37:44 AM] [INFO] 开始调用Gemini API {type: 'text', promptLength: 5407, options: {…}}
logger.js:472 [3:37:46 AM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [3:37:51 AM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [3:37:56 AM] [DEBUG] 状态已保存到本地存储 
logger.js:463 [3:37:59 AM] [ERROR] Gemini API调用失败 {type: 'text', error: 'timeoutId is not defined'}
outputToConsole @ logger.js:463
log @ logger.js:383
callAPI @ gemini-caller.js:161
await in callAPI
callGeminiAPI @ business-flow-controller.js:271
processInput @ business-flow-controller.js:189
await in processInput
parseOrder @ gemini-service-adapter.js:337
triggerRealtimeAnalysis @ realtime-analysis-manager.js:447
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:463 [3:37:59 AM] [ERROR] Gemini API调用失败 {error: 'timeoutId is not defined'}
outputToConsole @ logger.js:463
log @ logger.js:383
callGeminiAPI @ business-flow-controller.js:282
await in callGeminiAPI
processInput @ business-flow-controller.js:189
await in processInput
parseOrder @ gemini-service-adapter.js:337
triggerRealtimeAnalysis @ realtime-analysis-manager.js:447
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:463 [3:37:59 AM] [ERROR] 输入处理失败 {error: 'timeoutId is not defined', autoTriggered: false}
outputToConsole @ logger.js:463
log @ logger.js:383
processInput @ business-flow-controller.js:208
await in processInput
parseOrder @ gemini-service-adapter.js:337
triggerRealtimeAnalysis @ realtime-analysis-manager.js:447
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:463 [3:37:59 AM] [ERROR] 订单解析适配失败 {error: 'timeoutId is not defined'}
outputToConsole @ logger.js:463
log @ logger.js:383
parseOrder @ gemini-service-adapter.js:391
await in parseOrder
triggerRealtimeAnalysis @ realtime-analysis-manager.js:447
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:463 [3:37:59 AM] [ERROR] 实时订单解析失败 {type: 'error', error: '实时订单解析失败', context: {…}}
outputToConsole @ logger.js:463
log @ logger.js:383
logError @ logger.js:587
triggerRealtimeAnalysis @ realtime-analysis-manager.js:590
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:463 [3:37:59 AM] [ERROR] 实时分析失败 {error: 'timeoutId is not defined'}
outputToConsole @ logger.js:463
log @ logger.js:383
handleAnalysisError @ realtime-analysis-manager.js:719
triggerRealtimeAnalysis @ realtime-analysis-manager.js:591
await in triggerRealtimeAnalysis
(anonymous) @ realtime-analysis-manager.js:403
setTimeout
handleRealtimeInput @ realtime-analysis-manager.js:402
logger.js:472 [3:37:59 AM] [INFO] 🔄 实时分析处理完成 
logger.js:472 [3:38:01 AM] [DEBUG] 状态已保存到本地存储 
logger.js:472 [3:38:06 AM] [DEBUG] 状态已保存到本地存储 
