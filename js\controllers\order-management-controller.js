/**
 * ============================================================================
 * 🚀 核心业务流程 - 订单管理控制器 (母层架构)
 * ============================================================================
 *
 * @fileoverview 订单管理控制器 - 母层架构
 * @description 统一管理订单处理流程，协调订单相关的各个子层模块
 * 
 * @businessFlow 订单管理控制
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API → 结果处理
 *     ↓
 * B1. 单订单 → 【当前文件职责】映射到单订单表单 → 发送GoMyHire API
 * B2. 多订单 → 【当前文件职责】触发多订单模式 → 映射到多订单表单 → 批量发送API
 *     ↓
 * 【当前文件职责】保存到本地历史订单并持久化
 *
 * @architecture Mother Layer (母层)
 * - 职责：订单管理流程的统一控制和协调
 * - 原则：不包含具体实现逻辑，只负责调用和协调子层
 * - 接口：提供统一的订单管理API接口
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/business-flow-controller.js (业务流程控制器)
 * 下游依赖：
 * - order/multi-order-handler.js (多订单处理子层)
 * - order/api-caller.js (API调用子层)
 * - order/ui-controller.js (UI控制子层)
 * - order/history-manager.js (历史管理子层)
 *
 * @localProcessing 本地处理职责
 * - 订单管理流程控制和协调
 * - 子层模块的调用管理
 * - 单订单/多订单分支处理
 * - 错误处理和异常管理
 * - 状态管理和进度跟踪
 *
 * @remoteProcessing 远程处理职责
 * - 通过子层调用GoMyHire API
 * - 不直接进行远程调用
 *
 * @compatibility 兼容性保证
 * - 保持现有window.OTA.multiOrderManager接口
 * - 保持现有订单处理方法签名
 * - 提供向后兼容的适配器
 *
 * @refactoringConstraints 重构约束
 * - 必须保持现有订单处理逻辑
 * - 严格遵循母子两层架构原则
 * - 子层不能反向依赖母层
 * - 保持单向依赖关系
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 订单管理控制器 - 母层
     */
    class OrderManagementController {
        constructor() {
            this.logger = this.getLogger();
            this.multiOrderHandler = null;
            this.apiCaller = null;
            this.uiController = null;
            this.historyManager = null;
            
            // 初始化子层模块
            this.initializeChildLayers();
            
            this.logger.log('订单管理控制器已初始化', 'info');
        }

        /**
         * 初始化子层模块
         */
        initializeChildLayers() {
            try {
                // 延迟加载子层模块，避免循环依赖
                setTimeout(() => {
                    this.multiOrderHandler = window.OTA.MultiOrderHandler || null;
                    this.apiCaller = window.OTA.APICaller || null;
                    this.uiController = window.OTA.UIController || null;
                    this.historyManager = window.OTA.HistoryManager || null;
                    
                    if (!this.multiOrderHandler || !this.apiCaller || !this.uiController || !this.historyManager) {
                        this.logger.log('部分子层模块未加载，将使用降级方案', 'debug');
                    } else {
                        this.logger.log('所有子层模块加载成功', 'success');
                    }
                }, 100);
            } catch (error) {
                this.logger.log('子层模块初始化失败', 'error', { error: error.message });
            }
        }

        /**
         * 处理订单结果 - 主要入口
         * @param {object} processedResult - 业务流程处理结果
         * @param {object} options - 处理选项
         * @returns {Promise<object>} 处理结果
         */
        async handleOrderResult(processedResult, options = {}) {
            try {
                this.logger.log('开始处理订单结果', 'info', { 
                    type: processedResult.type,
                    orderCount: processedResult.orders?.length || 1
                });

                if (processedResult.type === 'single-order') {
                    return await this.handleSingleOrder(processedResult.order, options);
                } else if (processedResult.type === 'multi-order') {
                    return await this.handleMultiOrder(processedResult.orders, options);
                } else {
                    throw new Error(`未知的订单类型: ${processedResult.type}`);
                }

            } catch (error) {
                this.logger.log('订单结果处理失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 处理单订单
         * @param {object} order - 单个订单数据
         * @param {object} options - 处理选项
         * @returns {Promise<object>} 处理结果
         */
        async handleSingleOrder(order, options = {}) {
            try {
                this.logger.log('处理单订单', 'info', { 
                    customerName: order.customer_name 
                });

                // 步骤1：映射到单订单表单
                if (this.uiController) {
                    await this.uiController.mapToSingleOrderForm(order);
                } else {
                    await this.mapToSingleOrderFormFallback(order);
                }

                // 步骤2：发送GoMyHire API
                let apiResult = null;
                if (this.apiCaller && !options.skipAPI) {
                    apiResult = await this.apiCaller.createSingleOrder(order);
                }

                // 步骤3：保存到历史
                if (this.historyManager) {
                    await this.historyManager.saveOrder(order, apiResult);
                }

                this.logger.log('单订单处理完成', 'success');
                
                return {
                    type: 'single-order',
                    order: order,
                    apiResult: apiResult,
                    success: true
                };

            } catch (error) {
                this.logger.log('单订单处理失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 处理多订单
         * @param {array} orders - 多个订单数据
         * @param {object} options - 处理选项
         * @returns {Promise<object>} 处理结果
         */
        async handleMultiOrder(orders, options = {}) {
            try {
                this.logger.log('处理多订单', 'info', { 
                    orderCount: orders.length 
                });

                // 步骤1：触发多订单模式
                if (this.multiOrderHandler) {
                    await this.multiOrderHandler.activateMultiOrderMode(orders);
                } else {
                    await this.activateMultiOrderModeFallback(orders);
                }

                // 步骤2：映射到多订单表单
                if (this.uiController) {
                    await this.uiController.mapToMultiOrderForm(orders);
                }

                // 步骤3：批量处理（如果需要）
                let batchResult = null;
                if (this.apiCaller && options.autoBatch) {
                    batchResult = await this.apiCaller.createMultipleOrders(orders);
                }

                // 步骤4：保存到历史
                if (this.historyManager) {
                    await this.historyManager.saveMultipleOrders(orders, batchResult);
                }

                this.logger.log('多订单处理完成', 'success');
                
                return {
                    type: 'multi-order',
                    orders: orders,
                    batchResult: batchResult,
                    success: true
                };

            } catch (error) {
                this.logger.log('多订单处理失败', 'error', { error: error.message });
                throw error;
            }
        }

        // ========================================
        // 向后兼容的API接口
        // ========================================

        /**
         * 检测和处理多订单 - 兼容现有接口
         * @param {string} text - 订单文本
         * @param {object} options - 选项
         * @returns {Promise<object>} 处理结果
         */
        async detectAndHandleMultiOrders(text, options = {}) {
            try {
                // 委托给业务流程控制器
                const businessFlowController = window.OTA?.businessFlowController;
                if (businessFlowController) {
                    const result = await businessFlowController.processInput(text, 'text', { 
                        ...options, 
                        detectMultiOrder: true 
                    });
                    return await this.handleOrderResult(result, options);
                } else {
                    throw new Error('业务流程控制器未找到');
                }
            } catch (error) {
                this.logger.log('多订单检测和处理失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 显示多订单面板 - 兼容现有接口
         * @param {array} orders - 订单数组
         * @param {object} options - 选项
         */
        async showMultiOrderPanel(orders, options = {}) {
            if (this.uiController) {
                await this.uiController.showMultiOrderPanel(orders, options);
            } else {
                await this.showMultiOrderPanelFallback(orders, options);
            }
        }

        // ========================================
        // 降级方案
        // ========================================

        /**
         * 单订单表单映射降级方案
         */
        async mapToSingleOrderFormFallback(order) {
            this.logger.log('使用单订单表单映射降级方案', 'warning');
            // 基础的表单填充逻辑
            if (order.customer_name) {
                const nameField = document.querySelector('input[name="customer_name"]');
                if (nameField) nameField.value = order.customer_name;
            }
        }

        /**
         * 多订单模式激活降级方案
         */
        async activateMultiOrderModeFallback(orders) {
            this.logger.log('使用多订单模式激活降级方案', 'warning');
            // 基础的多订单处理逻辑
            console.log('多订单模式激活', orders);
        }

        /**
         * 多订单面板显示降级方案
         */
        async showMultiOrderPanelFallback(orders, options = {}) {
            this.logger.log('使用多订单面板显示降级方案', 'warning');
            // 基础的面板显示逻辑
            alert(`检测到 ${orders.length} 个订单`);
        }

        /**
         * 获取管理状态
         * @returns {object} 管理状态
         */
        getManagementStatus() {
            return {
                version: '2.0.0',
                architecture: 'mother-child',
                childLayers: {
                    multiOrderHandler: !!this.multiOrderHandler,
                    apiCaller: !!this.apiCaller,
                    uiController: !!this.uiController,
                    historyManager: !!this.historyManager
                }
            };
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const orderManagementController = new OrderManagementController();

    // 导出到全局作用域
    window.OrderManagementController = OrderManagementController;
    window.OTA.OrderManagementController = OrderManagementController;
    window.OTA.orderManagementController = orderManagementController;

    // 向后兼容：替换现有的multiOrderManager
    window.OTA.multiOrderManager = orderManagementController;
    window.multiOrderManager = orderManagementController;

    console.log('✅ OrderManagementController (母层控制器) 已加载');

})();
