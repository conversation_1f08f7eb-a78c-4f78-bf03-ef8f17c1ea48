<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复制功能字段映射修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 复制功能字段映射修复测试</h1>
    
    <div class="test-section">
        <h2>📊 修复前后对比</h2>
        <div class="result">
            <strong>修复前:</strong> ID=4 → 马六甲 (Malacca) ❌<br>
            <strong>修复后:</strong> ID=4 → Sabah (SBH) ✅
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 数据映射测试</h2>
        <button onclick="testDrivingRegionMapping()">测试驾驶区域映射</button>
        <button onclick="testCarTypeMapping()">测试车型映射</button>
        <button onclick="testServiceMapping()">测试服务类型映射</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📋 复制功能测试</h2>
        <button onclick="testCopyFunction()">测试复制功能</button>
        <div id="copyResults"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/core/script-loader.js"></script>
    
    <script>
        // 等待脚本加载完成
        setTimeout(() => {
            setupTests();
        }, 2000);

        function setupTests() {
            console.log('✅ 测试环境初始化完成');
        }

        function testDrivingRegionMapping() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h3>驾驶区域映射测试结果:</h3>';
            
            try {
                // 测试API服务的数据
                const apiService = getApiService();
                if (apiService && apiService.staticData && apiService.staticData.drivingRegions) {
                    const region4 = apiService.staticData.drivingRegions.find(r => r.id === 4);
                    if (region4) {
                        results.innerHTML += `<div class="success">✅ ID=4 正确映射: ${region4.name}</div>`;
                    } else {
                        results.innerHTML += `<div class="error">❌ 未找到ID=4的映射</div>`;
                    }
                    
                    // 显示所有驾驶区域
                    results.innerHTML += '<h4>所有驾驶区域:</h4>';
                    apiService.staticData.drivingRegions.forEach(region => {
                        results.innerHTML += `<div>ID=${region.id}: ${region.name}</div>`;
                    });
                } else {
                    results.innerHTML += `<div class="error">❌ 无法获取API服务数据</div>`;
                }
            } catch (error) {
                results.innerHTML += `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        function testCarTypeMapping() {
            const results = document.getElementById('testResults');
            results.innerHTML += '<h3>车型映射测试结果:</h3>';
            
            try {
                const apiService = getApiService();
                if (apiService && apiService.staticData && apiService.staticData.carTypes) {
                    results.innerHTML += `<div class="success">✅ 车型数据可用，共 ${apiService.staticData.carTypes.length} 种车型</div>`;
                    
                    // 测试几个常用车型
                    [5, 15, 33].forEach(id => {
                        const carType = apiService.staticData.carTypes.find(c => c.id === id);
                        if (carType) {
                            results.innerHTML += `<div>ID=${id}: ${carType.name}</div>`;
                        }
                    });
                } else {
                    results.innerHTML += `<div class="error">❌ 无法获取车型数据</div>`;
                }
            } catch (error) {
                results.innerHTML += `<div class="error">❌ 车型测试失败: ${error.message}</div>`;
            }
        }

        function testServiceMapping() {
            const results = document.getElementById('testResults');
            results.innerHTML += '<h3>服务类型映射测试结果:</h3>';
            
            try {
                const apiService = getApiService();
                if (apiService && apiService.staticData && apiService.staticData.subCategories) {
                    results.innerHTML += `<div class="success">✅ 服务类型数据可用，共 ${apiService.staticData.subCategories.length} 种服务</div>`;
                    
                    apiService.staticData.subCategories.forEach(service => {
                        results.innerHTML += `<div>ID=${service.id}: ${service.name}</div>`;
                    });
                } else {
                    results.innerHTML += `<div class="error">❌ 无法获取服务类型数据</div>`;
                }
            } catch (error) {
                results.innerHTML += `<div class="error">❌ 服务类型测试失败: ${error.message}</div>`;
            }
        }

        function testCopyFunction() {
            const results = document.getElementById('copyResults');
            results.innerHTML = '<h3>复制功能测试结果:</h3>';
            
            try {
                // 模拟一个测试订单数据
                const testOrder = {
                    id: 'test_001',
                    orderId: 'GMH_12345',
                    timestamp: new Date().toISOString(),
                    orderData: {
                        customerName: '测试客户',
                        drivingRegionId: 4,  // 关键：测试ID=4的映射
                        carTypeId: 5,
                        subCategoryId: 2,
                        otaReferenceNumber: 'TEST123',
                        pickup: '吉隆坡机场',
                        destination: '市中心酒店',
                        passengerNumber: 2
                    }
                };

                // 测试历史订单管理器的复制功能
                if (window.OTA && window.OTA.orderHistoryManager) {
                    // 临时添加测试订单
                    const originalGetOrderById = window.OTA.orderHistoryManager.getOrderById;
                    window.OTA.orderHistoryManager.getOrderById = function(id) {
                        if (id === 'test_001') return testOrder;
                        return originalGetOrderById.call(this, id);
                    };

                    // 测试复制功能
                    window.OTA.orderHistoryManager.copyOrderData('test_001').then(success => {
                        if (success) {
                            results.innerHTML += `<div class="success">✅ 复制功能测试成功</div>`;
                            results.innerHTML += `<div>📋 请检查剪贴板内容，驾驶区域应显示: "Sabah (SBH)"</div>`;
                        } else {
                            results.innerHTML += `<div class="error">❌ 复制功能测试失败</div>`;
                        }
                    }).catch(error => {
                        results.innerHTML += `<div class="error">❌ 复制测试异常: ${error.message}</div>`;
                    });

                    // 恢复原函数
                    window.OTA.orderHistoryManager.getOrderById = originalGetOrderById;
                } else {
                    results.innerHTML += `<div class="error">❌ 历史订单管理器未加载</div>`;
                }
            } catch (error) {
                results.innerHTML += `<div class="error">❌ 复制功能测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>