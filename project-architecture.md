# OTA订单处理系统 - 项目架构图

本文档展示了OTA订单处理系统的完整架构结构，采用母子两层架构模式，包含所有主要模块、组件和服务的依赖关系。

## 系统架构概览

```mermaid
flowchart TD
    %% 应用入口层
    subgraph "应用入口层"
        A1["`**应用入口**
        index.html`"]
        A2["`**主入口控制器**
        main.js`"]
        A3["`**脚本加载器**
        js/core/script-loader.js`"]
        A4["`**脚本清单**
        js/core/script-manifest.js`"]
    end

    %% 核心基础设施层
    subgraph "核心基础设施层"
        B1["`**依赖容器**
        js/core/dependency-container.js`"]
        B2["`**服务定位器**
        js/core/service-locator.js`"]
        B3["`**应用启动器**
        js/core/application-bootstrap.js`"]
        B4["`**OTA注册中心**
        js/core/ota-registry.js`"]
        B5["`**全局事件协调器**
        js/core/global-event-coordinator.js`"]
        B6["`**语言检测器**
        js/core/language-detector.js`"]
    end

    %% 母层控制器
    subgraph "母层控制器"
        C1["`**业务流程控制器**
        js/controllers/business-flow-controller.js`"]
        C2["`**订单管理控制器**
        js/controllers/order-management-controller.js`"]
    end

    %% 子层实现 - Flow子层
    subgraph "Flow子层 - 业务流程实现"
        D1["`**渠道检测器**
        js/flow/channel-detector.js`"]
        D2["`**提示词构建器**
        js/flow/prompt-builder.js`"]
        D3["`**Gemini调用器**
        js/flow/gemini-caller.js`"]
        D4["`**结果处理器**
        js/flow/result-processor.js`"]
        D5["`**订单解析器**
        js/flow/order-parser.js`"]
        D6["`**知识库管理**
        js/flow/knowledge-base.js`"]
        D7["`**地址翻译器**
        js/flow/address-translator.js`"]
    end

    %% 子层实现 - Order子层
    subgraph "Order子层 - 订单处理实现"
        E1["`**多订单处理器**
        js/order/multi-order-handler.js`"]
        E2["`**API调用器**
        js/order/api-caller.js`"]
        E3["`**历史管理器**
        js/order/history-manager.js`"]
    end

    %% 多订单系统
    subgraph "多订单系统"
        F1["`**多订单协调器**
        js/multi-order/multi-order-coordinator.js`"]
        F2["`**多订单状态管理器**
        js/multi-order/multi-order-state-manager.js`"]
        F3["`**多订单处理器**
        js/multi-order/multi-order-processor.js`"]
        F4["`**多订单渲染器**
        js/multi-order/multi-order-renderer.js`"]
        F5["`**批量处理器**
        js/multi-order/batch-processor.js`"]
    end

    %% 服务层
    subgraph "服务层"
        G1["`**API服务**
        js/api-service.js`"]
        G2["`**应用状态管理**
        js/app-state.js`"]
        G3["`**语言管理器**
        js/language-manager.js`"]
        G4["`**图像上传管理器**
        js/image-upload-manager.js`"]
        G5["`**航班信息服务**
        js/flight-info-service.js`"]
        G6["`**订单历史管理器**
        js/order-history-manager.js`"]
    end

    %% UI管理层
    subgraph "UI管理层"
        H1["`**UI管理器**
        js/ui-manager.js`"]
        H2["`**表单管理器**
        js/managers/form-manager.js`"]
        H3["`**价格管理器**
        js/managers/price-manager.js`"]
        H4["`**事件管理器**
        js/managers/event-manager.js`"]
        H5["`**UI状态管理器**
        js/managers/ui-state-manager.js`"]
        H6["`**动画管理器**
        js/managers/animation-manager.js`"]
        H7["`**OTA管理器**
        js/managers/ota-manager.js`"]
        H8["`**实时分析管理器**
        js/managers/realtime-analysis-manager.js`"]
    end

    %% 适配器层
    subgraph "适配器层 - 兼容性保证"
        I1["`**Gemini服务适配器**
        js/adapters/gemini-service-adapter.js`"]
        I2["`**多订单管理器适配器**
        js/adapters/multi-order-manager-adapter.js`"]
        I3["`**UI管理器适配器**
        js/adapters/ui-manager-adapter.js`"]
        I4["`**基础管理器适配器**
        js/adapters/base-manager-adapter.js`"]
        I5["`**OTA管理器装饰器**
        js/adapters/ota-manager-decorator.js`"]
    end

    %% 配置和策略层
    subgraph "配置和策略层"
        J1["`**OTA渠道配置**
        js/ota-channel-config.js`"]
        J2["`**OTA策略**
        js/ota-strategies.js`"]
        J3["`**酒店名称数据库**
        js/hotel-name-database.js`"]
        J4["`**酒店数据完整版**
        js/hotel-data-complete.js`"]
        J5["`**酒店数据内联版**
        js/hotel-data-inline.js`"]
    end

    %% 工具层
    subgraph "工具层"
        K1["`**通用工具**
        js/utils.js`"]
        K2["`**日志记录器**
        js/logger.js`"]
        K3["`**自动调整管理器**
        js/auto-resize-manager.js`"]
        K4["`**国际化**
        js/i18n.js`"]
    end

    %% 样式层
    subgraph "样式层"
        L1["`**主样式文件**
        css/main.css`"]
        L2["`**基础样式**
        css/base/`"]
        L3["`**组件样式**
        css/components/`"]
        L4["`**布局样式**
        css/layout/`"]
        L5["`**多订单样式**
        css/multi-order/`"]
    end

    %% 依赖关系
    A1 --> A2
    A2 --> A3
    A3 --> A4
    A4 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> B5
    B5 --> C1
    C1 --> C2
    C1 --> D1
    C1 --> D2
    C1 --> D3
    C1 --> D4
    C2 --> E1
    C2 --> E2
    C2 --> E3
    D3 --> D5
    D4 --> F1
    F1 --> F2
    F1 --> F3
    F1 --> F4
    F1 --> F5
    E1 --> F1
    C1 --> G1
    C2 --> G2
    B2 --> G3
    H1 --> H2
    H1 --> H3
    H1 --> H4
    H1 --> H5
    H8 --> D3
    I1 --> D3
    I2 --> F1
    I3 --> H1
    J1 --> D1
    J2 --> D2
    J3 --> D6
    K1 --> B1
    K2 --> B1
    L1 --> A1

    %% 样式定义
    classDef entryLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef coreLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef controllerLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef flowLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef orderLayer fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef multiOrderLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef serviceLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef uiLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef adapterLayer fill:#fafafa,stroke:#424242,stroke-width:2px
    classDef configLayer fill:#fff3e0,stroke:#bf360c,stroke-width:2px
    classDef utilLayer fill:#e8eaf6,stroke:#283593,stroke-width:2px
    classDef styleLayer fill:#f9fbe7,stroke:#827717,stroke-width:2px

    class A1,A2,A3,A4 entryLayer
    class B1,B2,B3,B4,B5,B6 coreLayer
    class C1,C2 controllerLayer
    class D1,D2,D3,D4,D5,D6,D7 flowLayer
    class E1,E2,E3 orderLayer
    class F1,F2,F3,F4,F5 multiOrderLayer
    class G1,G2,G3,G4,G5,G6 serviceLayer
    class H1,H2,H3,H4,H5,H6,H7,H8 uiLayer
    class I1,I2,I3,I4,I5 adapterLayer
    class J1,J2,J3,J4,J5 configLayer
    class K1,K2,K3,K4 utilLayer
    class L1,L2,L3,L4,L5 styleLayer
```

## 架构说明

### 1. 应用入口层
- **index.html**: 应用的HTML结构和UI布局
- **main.js**: 应用主入口，负责系统启动和初始化
- **script-loader.js**: 动态脚本加载器，管理模块加载顺序
- **script-manifest.js**: 脚本清单，定义加载阶段和依赖关系

### 2. 核心基础设施层
- **dependency-container.js**: 依赖注入容器，统一管理服务依赖
- **service-locator.js**: 服务定位器，提供服务获取接口
- **application-bootstrap.js**: 应用启动协调器，管理启动流程
- **ota-registry.js**: OTA注册中心，管理OTA相关配置
- **global-event-coordinator.js**: 全局事件协调器，处理跨模块通信
- **language-detector.js**: 语言检测器，智能检测和管理语言设置

### 3. 母层控制器
- **business-flow-controller.js**: 业务流程控制器，统一管理整个订单处理流程
- **order-management-controller.js**: 订单管理控制器，专门处理订单相关业务逻辑

### 4. 子层实现
#### Flow子层 - 业务流程实现
- **channel-detector.js**: 渠道检测，识别OTA平台类型
- **prompt-builder.js**: 提示词构建，组合AI分析提示词
- **gemini-caller.js**: Gemini API调用，处理AI分析请求
- **result-processor.js**: 结果处理，解析AI返回结果
- **order-parser.js**: 订单解析，提取订单信息
- **knowledge-base.js**: 知识库管理，维护酒店和地址数据
- **address-translator.js**: 地址翻译，处理多语言地址转换

#### Order子层 - 订单处理实现
- **multi-order-handler.js**: 多订单处理，管理批量订单操作
- **api-caller.js**: API调用，与GoMyHire后端通信
- **history-manager.js**: 历史管理，维护订单历史记录

### 5. 多订单系统
- **multi-order-coordinator.js**: 多订单协调器，统一管理多订单流程
- **multi-order-state-manager.js**: 状态管理，维护多订单状态
- **multi-order-processor.js**: 订单处理，执行批量订单创建
- **multi-order-renderer.js**: UI渲染，显示多订单界面
- **batch-processor.js**: 批量处理，执行批量操作

### 6. 服务层
- **api-service.js**: API服务，提供统一的后端接口
- **app-state.js**: 应用状态管理，维护全局应用状态
- **language-manager.js**: 语言管理，处理多语言支持
- **image-upload-manager.js**: 图像上传管理，处理图片上传和分析
- **flight-info-service.js**: 航班信息服务，提供航班数据查询
- **order-history-manager.js**: 订单历史管理，维护历史订单数据

### 7. UI管理层
- **ui-manager.js**: UI管理器，统一管理用户界面
- **form-manager.js**: 表单管理，处理表单验证和提交
- **price-manager.js**: 价格管理，计算和显示价格信息
- **event-manager.js**: 事件管理，处理用户交互事件
- **ui-state-manager.js**: UI状态管理，维护界面状态
- **animation-manager.js**: 动画管理，控制界面动画效果
- **ota-manager.js**: OTA管理，处理OTA相关UI逻辑
- **realtime-analysis-manager.js**: 实时分析管理，提供实时数据分析

### 8. 适配器层
- **gemini-service-adapter.js**: Gemini服务适配器，兼容旧版API
- **multi-order-manager-adapter.js**: 多订单管理器适配器，提供向后兼容
- **ui-manager-adapter.js**: UI管理器适配器，桥接新旧架构
- **base-manager-adapter.js**: 基础管理器适配器，提供通用适配功能
- **ota-manager-decorator.js**: OTA管理器装饰器，增强OTA功能

### 9. 配置和策略层
- **ota-channel-config.js**: OTA渠道配置，定义各OTA平台配置
- **ota-strategies.js**: OTA策略，实现平台特定的业务逻辑
- **hotel-name-database.js**: 酒店名称数据库，维护酒店名称映射
- **hotel-data-complete.js**: 完整酒店数据，提供详细酒店信息
- **hotel-data-inline.js**: 内联酒店数据，提供轻量级酒店数据

### 10. 工具层
- **utils.js**: 通用工具函数，提供常用工具方法
- **logger.js**: 日志记录器，统一日志管理
- **auto-resize-manager.js**: 自动调整管理器，处理界面自适应
- **i18n.js**: 国际化支持，提供多语言文本

### 11. 样式层
- **main.css**: 主样式文件，统一样式入口
- **base/**: 基础样式，包含重置和变量定义
- **components/**: 组件样式，定义UI组件样式
- **layout/**: 布局样式，定义页面布局
- **multi-order/**: 多订单样式，专门的多订单界面样式

## 关键特性

1. **母子两层架构**: 母层负责控制和协调，子层负责具体实现
2. **依赖注入**: 使用依赖容器统一管理服务依赖
3. **模块化设计**: 每个模块职责单一，便于维护和扩展
4. **适配器模式**: 提供向后兼容性，支持渐进式重构
5. **事件驱动**: 使用全局事件协调器实现模块间通信
6. **多订单支持**: 完整的多订单处理流程和UI支持
7. **AI集成**: 集成Gemini AI进行智能订单解析
8. **多语言支持**: 完整的国际化和本地化支持
