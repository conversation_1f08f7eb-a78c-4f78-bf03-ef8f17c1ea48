/**
 * ============================================================================
 * 🔐 权限管理器
 * ============================================================================
 * 
 * @fileoverview 统一的权限管理系统
 * @description 提供统一的权限检查接口，替代分散在各文件中的权限逻辑
 * 
 * @dependencies js/config/user-permissions-config.js
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-12
 * @lastModified 2025-08-12
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    /**
     * 权限管理器类
     * 提供统一的权限检查和管理功能
     */
    class PermissionManager {
        constructor() {
            this.logger = this.getLogger();
            // 🚀 简化：权限相对稳定，保留简单缓存以提高性能
            this.cache = new Map(); // 权限缓存
            this.cacheTimeout = 10 * 60 * 1000; // 10分钟缓存，权限变更不频繁
            
            this.initialized = false;
            this.init();
        }

        /**
         * 初始化权限管理器
         */
        init() {
            try {
                // 检查权限配置是否已加载
                if (!window.OTA?.config?.USER_PERMISSION_CONFIG) {
                    throw new Error('权限配置未加载，请确保user-permissions-config.js已正确加载');
                }

                this.config = window.OTA.config.USER_PERMISSION_CONFIG;
                this.initialized = true;

                this.logger.log('权限管理器初始化完成', 'success', {
                    version: this.config.config.version,
                    enabled: this.config.config.enabled
                });

            } catch (error) {
                this.logger.log('权限管理器初始化失败', 'error', { error: error.message });
            }
        }

        /**
         * 获取当前登录用户信息
         * @returns {object|null} 用户信息
         */
        getCurrentUser() {
            try {
                // 从AppState获取用户信息
                if (typeof getAppState === 'function') {
                    return getAppState().get('auth.user');
                }
                
                // 备用方法：从全局变量获取
                if (window.currentUser) {
                    return window.currentUser;
                }

                return null;
            } catch (error) {
                this.logger.log('获取当前用户失败', 'warning', { error: error.message });
                return null;
            }
        }

        /**
         * 获取用户标识（邮箱或ID）
         * @param {object} user - 用户对象
         * @returns {string|number|null} 用户标识
         */
        getUserIdentifier(user = null) {
            const currentUser = user || this.getCurrentUser();
            
            if (!currentUser) {
                return null;
            }

            // 优先使用邮箱
            if (currentUser.email) {
                return currentUser.email.toLowerCase();
            }

            // 备用：使用用户ID
            if (currentUser.id) {
                return currentUser.id;
            }

            // 备用：使用backend_user_id
            if (currentUser.backend_user_id) {
                return currentUser.backend_user_id;
            }

            return null;
        }

        /**
         * 获取用户权限（带缓存）
         * @param {string|number} userIdentifier - 用户标识
         * @returns {object} 用户权限配置
         */
        getUserPermissions(userIdentifier = null) {
            if (!this.initialized) {
                this.logger.log('权限管理器未初始化，返回默认权限', 'warning');
                return this.getDefaultPermissions();
            }

            const identifier = userIdentifier || this.getUserIdentifier();
            
            if (!identifier) {
                this.logger.log('无法获取用户标识，返回默认权限', 'warning');
                return this.getDefaultPermissions();
            }

            // 检查缓存
            const cacheKey = `permissions_${identifier}`;
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    return cached.permissions;
                }
            }

            // 获取权限配置
            const permissions = window.OTA.config.getUserPermissions(identifier);
            
            // 缓存权限
            this.cache.set(cacheKey, {
                permissions,
                timestamp: Date.now()
            });

            this.logger.log('用户权限已获取', 'debug', {
                identifier: String(identifier).substring(0, 10) + '...',
                hasRestrictions: this.hasAnyRestrictions(permissions),
                cached: this.cache.has(`permissions_${identifier}`)
            });

            return permissions;
        }

        /**
         * 获取默认权限
         * @returns {object} 默认权限配置
         */
        getDefaultPermissions() {
            return {
                priceDisplay: {
                    canViewOtaPrice: true,
                    canViewDriverFee: true
                },
                features: {
                    canUsePaging: true
                },
                channels: {
                    restricted: false,
                    allowedChannels: null
                }
            };
        }

        /**
         * 检查价格字段权限
         * @param {string|number} userIdentifier - 用户标识（可选）
         * @returns {object} 价格权限对象
         */
        checkPriceFieldPermissions(userIdentifier = null) {
            const permissions = this.getUserPermissions(userIdentifier);
            
            const pricePermissions = {
                canViewOtaPrice: permissions.priceDisplay?.canViewOtaPrice ?? true,
                canViewDriverFee: permissions.priceDisplay?.canViewDriverFee ?? true
            };

            this.logger.log('价格字段权限检查', 'info', {
                email: this.getUserIdentifier()?.toString().substring(0, 15) + '...',
                permissions: pricePermissions
            });

            return pricePermissions;
        }

        /**
         * 检查语言选项权限（Paging）
         * @param {string|number} userIdentifier - 用户标识（可选）
         * @returns {object} 语言选项权限对象
         */
        checkLanguagePermissions(userIdentifier = null) {
            const permissions = this.getUserPermissions(userIdentifier);
            
            const languagePermissions = {
                canUsePaging: permissions.features?.canUsePaging ?? true
            };

            this.logger.log('语言选项权限检查', 'info', {
                email: this.getUserIdentifier()?.toString().substring(0, 15) + '...',
                permissions: languagePermissions
            });

            return languagePermissions;
        }

        /**
         * 检查渠道权限
         * @param {string|number} userIdentifier - 用户标识（可选）
         * @returns {object} 渠道权限对象
         */
        checkChannelPermissions(userIdentifier = null) {
            const permissions = this.getUserPermissions(userIdentifier);
            
            const channelPermissions = {
                restricted: permissions.channels?.restricted ?? false,
                allowedChannels: permissions.channels?.allowedChannels ?? null
            };

            this.logger.log('渠道权限检查', 'info', {
                email: this.getUserIdentifier()?.toString().substring(0, 15) + '...',
                restricted: channelPermissions.restricted,
                allowedChannelsCount: channelPermissions.allowedChannels?.length || 'unlimited'
            });

            return channelPermissions;
        }

        /**
         * 获取用户允许的渠道列表
         * @param {array} allChannels - 所有可用渠道
         * @param {string|number} userIdentifier - 用户标识（可选）
         * @returns {array} 过滤后的渠道列表
         */
        getFilteredChannels(allChannels, userIdentifier = null) {
            const channelPermissions = this.checkChannelPermissions(userIdentifier);
            
            if (!channelPermissions.restricted) {
                // 无限制，返回所有渠道
                return allChannels;
            }

            if (!channelPermissions.allowedChannels || channelPermissions.allowedChannels.length === 0) {
                // 完全限制，返回空数组
                return [];
            }

            // 过滤渠道
            const allowedSet = new Set(channelPermissions.allowedChannels);
            const filteredChannels = allChannels.filter(channel => {
                const channelValue = typeof channel === 'string' ? channel : channel.value || channel.text;
                return allowedSet.has(channelValue);
            });

            this.logger.log('渠道列表已过滤', 'info', {
                originalCount: allChannels.length,
                filteredCount: filteredChannels.length,
                allowedChannels: channelPermissions.allowedChannels
            });

            return filteredChannels;
        }

        /**
         * 检查用户是否有任何限制
         * @param {object} permissions - 权限对象
         * @returns {boolean} 是否有限制
         */
        hasAnyRestrictions(permissions) {
            if (!permissions) return false;

            // 检查价格限制
            if (!permissions.priceDisplay?.canViewOtaPrice || !permissions.priceDisplay?.canViewDriverFee) {
                return true;
            }

            // 检查功能限制
            if (!permissions.features?.canUsePaging) {
                return true;
            }

            // 检查渠道限制
            if (permissions.channels?.restricted) {
                return true;
            }

            return false;
        }

        /**
         * 重新应用用户权限
         * @description 在用户登录或权限变更后调用
         */
        reapplyUserPermissions() {
            try {
                this.logger.log('开始重新应用用户权限', 'info');

                // 清理缓存
                this.clearCache();

                const currentUser = this.getCurrentUser();
                if (!currentUser) {
                    this.logger.log('用户未登录，跳过权限重新应用', 'info');
                    return;
                }

                // 获取form manager并应用权限
                const formManager = this.getFormManager();
                if (formManager) {
                    // 应用价格权限
                    if (typeof formManager.initializePriceFieldPermissions === 'function') {
                        formManager.initializePriceFieldPermissions();
                    }

                    // 应用语言权限
                    if (typeof formManager.initializeLanguagePermissions === 'function') {
                        formManager.initializeLanguagePermissions();
                    }

                    // 应用渠道权限
                    if (typeof formManager.initializeChannelPermissions === 'function') {
                        formManager.initializeChannelPermissions();
                    } else {
                        getLogger().log('FormManager 缺少 initializeChannelPermissions 方法', 'warning');
                    }
                }

                this.logger.log('用户权限重新应用完成', 'success');

            } catch (error) {
                this.logger.log('重新应用用户权限失败', 'error', { error: error.message });
            }
        }

        /**
         * 清理权限缓存
         */
        clearCache() {
            this.cache.clear();
            this.logger.log('权限缓存已清理', 'debug');
        }

        /**
         * 获取权限统计信息
         * @returns {object} 权限统计
         */
        getPermissionStats() {
            const currentUser = this.getCurrentUser();
            const permissions = this.getUserPermissions();
            
            return {
                userIdentified: !!currentUser,
                userEmail: currentUser?.email || 'unknown',
                hasRestrictions: this.hasAnyRestrictions(permissions),
                priceRestricted: !permissions.priceDisplay?.canViewOtaPrice || !permissions.priceDisplay?.canViewDriverFee,
                pagingRestricted: !permissions.features?.canUsePaging,
                channelRestricted: permissions.channels?.restricted,
                cacheSize: this.cache.size,
                configVersion: this.config?.config?.version || 'unknown'
            };
        }

        /**
         * 获取FormManager实例
         * @returns {object|null} FormManager实例
         */
        getFormManager() {
            // 尝试多种方式获取FormManager
            if (window.formManager) {
                return window.formManager;
            }

            if (window.uiManager?.managers?.form) {
                return window.uiManager.managers.form;
            }

            if (window.OTA?.managers?.FormManager) {
                return new window.OTA.managers.FormManager();
            }

            return null;
        }

        /**
         * 获取Logger实例
         * @returns {object} Logger实例
         */
        getLogger() {
            if (typeof getLogger === 'function') {
                return getLogger();
            }

            return {
                log: (message, level = 'info', data = {}) => {
                    const timestamp = new Date().toLocaleTimeString();
                    console.log(`[${timestamp}] [PERMISSION][${level.toUpperCase()}] ${message}`, data);
                }
            };
        }
    }

    // 创建全局实例
    const permissionManager = new PermissionManager();

    // 导出到全局作用域
    window.OTA.managers.PermissionManager = PermissionManager;
    window.OTA.managers.permissionManager = permissionManager;
    window.permissionManager = permissionManager; // 向后兼容

    // 注册到服务定位器（如果存在）
    if (window.OTA?.Registry) {
        window.OTA.Registry.registerService('permissionManager', permissionManager, '@PERMISSION_MANAGER');
    }

    console.log('✅ 权限管理器已加载');

})();