# OTA订单处理系统 - 数据流图

本文档展示了OTA订单处理系统中数据的完整流转过程，包含数据的输入源、处理节点、存储位置和输出目标。

## 系统数据流概览

```mermaid
flowchart LR
    %% 用户输入层
    subgraph "用户输入层"
        A1["`**文本输入**
        textarea#orderInput`"]
        A2["`**图片上传**
        input#imageFileInput`"]
        A3["`**表单数据**
        form#orderForm`"]
    end

    %% 输入处理层
    subgraph "输入处理层"
        B1["`**输入验证**
        js/managers/form-manager.js`"]
        B2["`**图像预处理**
        js/image-upload-manager.js`"]
        B3["`**渠道检测**
        js/flow/channel-detector.js`"]
        B4["`**语言检测**
        js/core/language-detector.js`"]
    end

    %% AI处理层
    subgraph "AI处理层"
        C1["`**提示词构建**
        js/flow/prompt-builder.js`"]
        C2["`**Gemini API调用**
        js/flow/gemini-caller.js`"]
        C3["`**结果解析**
        js/flow/result-processor.js`"]
        C4["`**订单解析**
        js/flow/order-parser.js`"]
    end

    %% 业务逻辑层
    subgraph "业务逻辑层"
        D1["`**单订单处理**
        js/controllers/order-management-controller.js`"]
        D2["`**多订单检测**
        js/multi-order/multi-order-coordinator.js`"]
        D3["`**多订单处理**
        js/multi-order/multi-order-processor.js`"]
        D4["`**批量处理**
        js/multi-order/batch-processor.js`"]
    end

    %% 数据转换层
    subgraph "数据转换层"
        E1["`**表单映射**
        js/managers/form-manager.js`"]
        E2["`**价格计算**
        js/managers/price-manager.js`"]
        E3["`**OTA策略应用**
        js/ota-strategies.js`"]
        E4["`**数据验证**
        js/utils.js`"]
    end

    %% API调用层
    subgraph "API调用层"
        F1["`**订单创建API**
        js/api-service.js`"]
        F2["`**系统数据获取**
        js/api-service.js`"]
        F3["`**航班信息查询**
        js/flight-info-service.js`"]
        F4["`**地址翻译**
        js/flow/address-translator.js`"]
    end

    %% 状态管理层
    subgraph "状态管理层"
        G1["`**应用状态**
        js/app-state.js`"]
        G2["`**多订单状态**
        js/multi-order/multi-order-state-manager.js`"]
        G3["`**UI状态**
        js/managers/ui-state-manager.js`"]
        G4["`**历史状态**
        js/order-history-manager.js`"]
    end

    %% 存储层
    subgraph "存储层"
        H1["`**本地存储**
        localStorage`"]
        H2["`**会话存储**
        sessionStorage`"]
        H3["`**内存缓存**
        Map/Set对象`"]
        H4["`**历史记录**
        IndexedDB`"]
    end

    %% UI更新层
    subgraph "UI更新层"
        I1["`**表单更新**
        js/ui-manager.js`"]
        I2["`**多订单界面**
        js/multi-order/multi-order-renderer.js`"]
        I3["`**状态显示**
        footer.status-bar`"]
        I4["`**历史面板**
        div#historyPanel`"]
    end

    %% 外部系统
    subgraph "外部系统"
        J1["`**GoMyHire API**
        https://gomyhire.com/api`"]
        J2["`**Gemini AI API**
        Google AI Platform`"]
        J3["`**航班数据API**
        第三方航班服务`"]
    end

    %% 主要数据流路径
    A1 --> B1
    A2 --> B2
    A3 --> B1
    
    B1 --> B3
    B1 --> B4
    B2 --> C1
    B3 --> C1
    B4 --> C1
    
    C1 --> C2
    C2 --> J2
    J2 --> C3
    C3 --> C4
    
    C4 --> D1
    C4 --> D2
    D2 --> D3
    D3 --> D4
    
    D1 --> E1
    D3 --> E1
    E1 --> E2
    E2 --> E3
    E3 --> E4
    
    E4 --> F1
    F1 --> J1
    F2 --> J1
    F3 --> J3
    F4 --> J1
    
    D1 --> G1
    D2 --> G2
    E1 --> G3
    F1 --> G4
    
    G1 --> H1
    G2 --> H2
    G3 --> H3
    G4 --> H4
    
    G1 --> I1
    G2 --> I2
    G3 --> I3
    G4 --> I4

    %% 反馈循环
    I1 --> A3
    I2 --> D3
    J1 --> G4
    G4 --> I4

    %% 样式定义
    classDef inputLayer fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef processLayer fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef aiLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef businessLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef transformLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef apiLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef stateLayer fill:#fff8e1,stroke:#fbc02d,stroke-width:2px
    classDef storageLayer fill:#efebe9,stroke:#5d4037,stroke-width:2px
    classDef uiLayer fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef externalLayer fill:#fafafa,stroke:#616161,stroke-width:2px

    class A1,A2,A3 inputLayer
    class B1,B2,B3,B4 processLayer
    class C1,C2,C3,C4 aiLayer
    class D1,D2,D3,D4 businessLayer
    class E1,E2,E3,E4 transformLayer
    class F1,F2,F3,F4 apiLayer
    class G1,G2,G3,G4 stateLayer
    class H1,H2,H3,H4 storageLayer
    class I1,I2,I3,I4 uiLayer
    class J1,J2,J3 externalLayer
```

## 详细数据流说明

### 1. 用户输入阶段
**数据源**: 用户界面输入
- **文本输入**: 用户在textarea中输入订单描述文本
- **图片上传**: 用户上传订单截图或相关图片
- **表单数据**: 用户手动填写的表单字段数据

**处理文件**: 
- `index.html` - 提供输入界面
- `js/ui-manager.js` - 管理用户交互

### 2. 输入处理阶段
**数据转换**: 原始输入 → 结构化数据
- **输入验证**: 检查数据格式和完整性
- **图像预处理**: 压缩、格式转换、OCR准备
- **渠道检测**: 基于文本特征识别OTA平台
- **语言检测**: 自动识别输入文本的语言

**处理文件**:
- `js/managers/form-manager.js` - 表单验证和处理
- `js/image-upload-manager.js` - 图像处理
- `js/flow/channel-detector.js` - OTA渠道识别
- `js/core/language-detector.js` - 语言识别

### 3. AI处理阶段
**数据转换**: 结构化数据 → AI分析结果
- **提示词构建**: 根据检测结果组合AI提示词
- **Gemini API调用**: 发送数据到Google AI进行分析
- **结果解析**: 解析AI返回的JSON结果
- **订单解析**: 提取具体的订单信息字段

**处理文件**:
- `js/flow/prompt-builder.js` - 构建AI提示词
- `js/flow/gemini-caller.js` - 调用Gemini API
- `js/flow/result-processor.js` - 处理AI结果
- `js/flow/order-parser.js` - 解析订单数据

### 4. 业务逻辑阶段
**数据转换**: 订单信息 → 业务对象
- **单订单处理**: 处理单个订单的业务逻辑
- **多订单检测**: 检测是否包含多个订单
- **多订单处理**: 分离和处理多个订单
- **批量处理**: 执行批量订单操作

**处理文件**:
- `js/controllers/order-management-controller.js` - 订单管理
- `js/multi-order/multi-order-coordinator.js` - 多订单协调
- `js/multi-order/multi-order-processor.js` - 多订单处理
- `js/multi-order/batch-processor.js` - 批量处理

### 5. 数据转换阶段
**数据转换**: 业务对象 → API请求数据
- **表单映射**: 将解析数据映射到表单字段
- **价格计算**: 根据OTA策略计算价格
- **OTA策略应用**: 应用平台特定的业务规则
- **数据验证**: 最终验证数据完整性和正确性

**处理文件**:
- `js/managers/form-manager.js` - 表单字段映射
- `js/managers/price-manager.js` - 价格计算逻辑
- `js/ota-strategies.js` - OTA平台策略
- `js/utils.js` - 数据验证工具

### 6. API调用阶段
**数据转换**: API请求数据 → 外部系统响应
- **订单创建API**: 调用GoMyHire API创建订单
- **系统数据获取**: 获取系统配置和基础数据
- **航班信息查询**: 查询航班相关信息
- **地址翻译**: 翻译和标准化地址信息

**处理文件**:
- `js/api-service.js` - 统一API调用服务
- `js/flight-info-service.js` - 航班信息服务
- `js/flow/address-translator.js` - 地址翻译服务

### 7. 状态管理阶段
**数据转换**: 处理结果 → 应用状态
- **应用状态**: 维护全局应用状态
- **多订单状态**: 管理多订单处理状态
- **UI状态**: 控制界面显示状态
- **历史状态**: 记录订单历史和操作记录

**处理文件**:
- `js/app-state.js` - 全局状态管理
- `js/multi-order/multi-order-state-manager.js` - 多订单状态
- `js/managers/ui-state-manager.js` - UI状态管理
- `js/order-history-manager.js` - 历史记录管理

### 8. 存储阶段
**数据转换**: 应用状态 → 持久化存储
- **本地存储**: 保存用户设置和配置
- **会话存储**: 保存临时会话数据
- **内存缓存**: 缓存频繁访问的数据
- **历史记录**: 持久化订单历史数据

**存储位置**:
- `localStorage` - 持久化用户设置
- `sessionStorage` - 临时会话数据
- 内存Map/Set对象 - 运行时缓存
- `IndexedDB` - 大量历史数据存储

### 9. UI更新阶段
**数据转换**: 应用状态 → 用户界面
- **表单更新**: 更新表单字段显示
- **多订单界面**: 渲染多订单管理界面
- **状态显示**: 更新系统状态指示器
- **历史面板**: 显示历史订单列表

**处理文件**:
- `js/ui-manager.js` - 主界面更新
- `js/multi-order/multi-order-renderer.js` - 多订单界面
- 状态栏元素 - 系统状态显示
- 历史面板元素 - 历史记录显示

## 关键数据流路径

### 主要处理流程
1. **用户输入** → **输入处理** → **AI分析** → **业务处理** → **数据转换** → **API调用** → **状态更新** → **UI刷新**

### 单订单流程
```
文本输入 → 渠道检测 → AI解析 → 单订单处理 → 表单映射 → 订单创建 → 历史保存 → 界面更新
```

### 多订单流程
```
文本输入 → 渠道检测 → AI解析 → 多订单检测 → 订单分离 → 批量处理 → 状态管理 → 多订单界面
```

### 图片处理流程
```
图片上传 → 图像预处理 → OCR识别 → AI分析 → 结果合并 → 正常订单流程
```

## 数据格式转换

### 输入数据格式
```javascript
// 原始文本输入
{
  text: "订单描述文本...",
  images: [File对象数组],
  formData: {表单字段对象}
}
```

### AI处理数据格式
```javascript
// Gemini API请求格式
{
  prompt: "构建的提示词",
  context: {渠道信息, 语言信息},
  images: [base64编码图片]
}

// Gemini API响应格式
{
  orders: [订单对象数组],
  confidence: 置信度,
  metadata: {元数据}
}
```

### API调用数据格式
```javascript
// GoMyHire API请求格式
{
  customerName: "客户姓名",
  pickup: "上车地点",
  dropoff: "目的地",
  pickupDate: "接送日期",
  // ... 其他字段
}
```

### 状态数据格式
```javascript
// 应用状态格式
{
  auth: {认证信息},
  currentOrder: {当前订单},
  multiOrder: {多订单状态},
  ui: {界面状态},
  system: {系统状态}
}
```

## 性能优化

1. **异步处理**: 所有API调用和文件处理都采用异步方式
2. **数据缓存**: 频繁访问的数据进行内存缓存
3. **批量操作**: 多订单支持批量处理以提高效率
4. **状态管理**: 使用集中式状态管理避免数据不一致
5. **错误处理**: 完整的错误处理和恢复机制

## 安全考虑

1. **数据验证**: 所有输入数据都经过严格验证
2. **API安全**: 使用认证令牌保护API调用
3. **本地存储**: 敏感数据加密存储
4. **错误日志**: 安全的错误日志记录机制
